"use client";

import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableApiKey from "@/modules/api-key/components/table-api-key";
import TableApiKeyAccount from "@/modules/api-key/components/table-api-key-account";
import { useAppSelector } from "@/common/vendors/redux/store/hook";

export default function Page() {
  const userInfo = useAppSelector((state) => state.user.userInfo);
  const isAdmin = userInfo?.roles?.includes(PERMISSIONS.Admin.value);

  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      {isAdmin ? <TableApiKey /> : <TableApiKeyAccount />}
    </PermissionProvider>
  );
}
