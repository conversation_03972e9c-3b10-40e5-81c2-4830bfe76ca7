import { ButtonCommon } from "@/components/common/button-common";

export const SearchInput = ({
  count,
  loading,
  disabled,
  onClick,
  icon,
  action,
}: {
  count: number;
  loading: boolean;
  disabled: boolean;
  onClick: () => void;
  icon: React.ReactNode;
  action: string;
}) => {
  return (
    <ButtonCommon
      disabled={disabled}
      loading={loading}
      onClick={onClick}
      className="btn btn-sm bg-primary text-white hover:bg-primary w-full"
    >
      {icon} {action} {count} users
    </ButtonCommon>
  );
};
