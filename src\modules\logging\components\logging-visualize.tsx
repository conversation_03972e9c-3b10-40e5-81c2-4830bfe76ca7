import useDetectScreenSize from "@/common/hooks/useDetectScreenSize";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import ZoomableImage from "@/modules/image/components/canvas/zoomable-image";
import getAllDataEntryByLoggingView from "@/modules/logging-view/api/data-entry.api";
import { ColumnClass } from "@/modules/logging-view/enum/enum";
import {
  useGetListLoggingView,
  useGetListLoggingViewColumn,
} from "@/modules/logging-view/hooks/useGetListLoggingView";
import {
  ReloadOutlined,
  SearchOutlined,
  SettingOutlined,
} from "@ant-design/icons";
import {
  Button,
  Divider,
  Image,
  InputNumber,
  Popover,
  Select,
  Space,
  Spin,
} from "antd";
import { Layer as KonvaLayer } from "konva/lib/Layer";
import { KonvaEventObject } from "konva/lib/Node";
import { Stage as KonvaStage } from "konva/lib/Stage";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Group, Layer, Rect, Stage, Text } from "react-konva";
import { AutoSizer } from "react-virtualized";
import { CONFIG } from "../constants/logging.constants";
import {
  updateDisplayColumn,
  updateOffsetY,
} from "../redux/loggingSlice/logging.slice";
import { processLoggingViewData } from "../utils/logging.utils";
import { AssayData } from "./assay-data";
import { ColumnSettings } from "./column-settings";
import { GeologyData } from "./geology-data";
import { GeophysicsData } from "./geophysics-data";
import { useRouter, useSearchParams } from "next/navigation";

const AutoSizerTmp: any = AutoSizer;

export function LoggingVisualize() {
  const searchParams = useSearchParams();
  const router = useRouter();

  // Get individual params
  const projectId = searchParams.get("projectId");
  const prospectId = searchParams.get("prospectId");
  const activeKey = searchParams.get("activeKey");
  const loggingViewIdParams = searchParams.get("loggingViewId");
  const depthInputParams = searchParams.get("depthInput");

  const params = new URLSearchParams();
  const queries = {
    projectId: projectId,
    prospectId: prospectId,
    activeKey: activeKey,
    loggingViewId: loggingViewIdParams,
    depthInput: depthInputParams,
  };

  // Only add params that have values
  Object.entries(queries).forEach(([key, value]) => {
    if (value) {
      params.set(key, value);
    }
  });

  const dispatch = useAppDispatch();
  const layerRef = useRef<KonvaLayer>(null);
  const stageRef = useRef<KonvaStage>(null);

  const drillholeDetailId = useAppSelector(
    (state) => state.logging.selectedDrillHole?.value
  );

  const { height: screenHeight } = useDetectScreenSize();
  const displayColumn =
    useAppSelector((state) => state.logging.displayColumn) ||
    CONFIG.DEFAULT_DISPLAY_COLUMN;

  // Use the targetDepth from Redux if available
  const { offsetY: currentOffsetY, targetDepth: storedTargetDepth } =
    useAppSelector((state) => state.logging);
  const [depthInput, setDepthInput] = useState<string>(
    storedTargetDepth !== undefined ? storedTargetDepth.toString() : ""
  );

  // Local target depth for scale changes within the component
  const [targetDepth, setTargetDepth] = useState<number | null>(
    storedTargetDepth !== undefined ? storedTargetDepth : null
  );

  const { request: requestGetListLoggingView, data: loggingViews } =
    useGetListLoggingView();
  const { request: requestGetLoggingViewColumn, data: loggingViewColumn } =
    useGetListLoggingViewColumn();

  const [loggingViewData, setLoggingViewData] = useState<any>();

  // const [loggingViewId, setLoggingViewId] = useState<string>();
  const [isLoading, setIsLoading] = useState(false);
  const [lastCenter, setLastCenter] = useState<{ x: number; y: number } | null>(
    null
  );
  const [lastDist, setLastDist] = useState<number>(0);
  const [isZooming, setIsZooming] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string>("");
  const touchStartTime = useRef<number>(0);
  const initialTouchCenterRef = useRef<{ x: number; y: number } | null>(null);

  const getDistance = useCallback(
    (p1: { x: number; y: number }, p2: { x: number; y: number }) => {
      return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
    },
    []
  );

  const getCenter = useCallback(
    (p1: { x: number; y: number }, p2: { x: number; y: number }) => {
      return {
        x: (p1.x + p2.x) / 2,
        y: (p1.y + p2.y) / 2,
      };
    },
    []
  );

  const handleOnWheelStage = (e: KonvaEventObject<WheelEvent>) => {
    e.evt.preventDefault();
    const stage = stageRef.current;
    const layer = layerRef.current;
    if (!layer || !stage) return;

    const oldScale = stage.scaleX();
    const pointer = stage.getPointerPosition();
    if (!pointer) return;

    const mousePointTo = {
      x: pointer.x / oldScale - stage.x() / oldScale,
      y: pointer.y / oldScale - stage.y() / oldScale,
    };

    const direction = e.evt.deltaY > 0 ? -1 : 1;
    const newScale =
      direction > 0 ? oldScale * CONFIG.SCALE_BY : oldScale / CONFIG.SCALE_BY;

    stage.scale({ x: newScale, y: newScale });
    const newPos = {
      x: -(mousePointTo.x - pointer.x / newScale) * newScale,
      y: -(mousePointTo.y - pointer.y / newScale) * newScale,
    };
    stage.position(newPos);
    stage.batchDraw();

    // Handle the scale change in a short delay to ensure stage is updated
    setTimeout(() => handleScaleChange(), 0);
  };

  const onSelectLoggingView = async (selectedLoggingViewId: string) => {
    try {
      setIsLoading(true);
      // setLoggingViewId(selectedLoggingViewId);
      params.set("loggingViewId", selectedLoggingViewId);
      router.push(`${window.location.pathname}?${params.toString()}`);
      setLoggingViewData(undefined);
      // Keeping the offset when changing logging view (offsetY is persisted in Redux)

      // First call to get total count
      const initialResponse = await requestGetLoggingViewColumn({
        loggingViewId: selectedLoggingViewId,
        skipCount: 0,
        maxResultCount: 1,
      });

      if (!initialResponse?.items || !drillholeDetailId) {
        return;
      }

      const totalCount = initialResponse.pagination?.total || 0;
      // Call the request with combined data to update the hook state
      await requestGetLoggingViewColumn({
        loggingViewId: selectedLoggingViewId,
        skipCount: 0,
        maxResultCount: totalCount,
      });

      const res = await getAllDataEntryByLoggingView.getList({
        loggingViewId: Number(selectedLoggingViewId),
        drillHoleId: Number(drillholeDetailId),
      });

      if (res?.data) {
        setLoggingViewData(res.data);
      }
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  const result = useMemo(() => {
    if (!loggingViewData || !loggingViewColumn) return [];
    if (loggingViewColumn) {
      loggingViewColumn.sort((a, b) => (a.sequence || 0) - (b.sequence || 0));
    }

    return processLoggingViewData(
      loggingViewData,
      loggingViewColumn,
      CONFIG.DEFAULT_PADDING * 2
    );
  }, [loggingViewData, loggingViewColumn]);
  const { maxHeight, startY } = useMemo(() => {
    if (!result.length) return { maxHeight: 0, startY: 0 };

    let minDepth = Infinity;
    let maxDepth = -Infinity;

    result.forEach((info) => {
      switch (info?.columnClass) {
        case ColumnClass.Geophysics:
          if (info?.data?.length > 0) {
            const depths = info.data.map((item: any) =>
              Number(item["Depth (m)"])
            );
            minDepth = Math.min(minDepth, ...depths);
            maxDepth = Math.max(maxDepth, ...depths);
          }
          break;
        case ColumnClass.Assay:
          if (info?.data?.length > 0) {
            const depths = info.data
              .map((item: any) => [
                Number(item["Depth From"]),
                Number(item["Depth To"]),
              ])
              .flat();
            minDepth = Math.min(minDepth, ...depths);
            maxDepth = Math.max(maxDepth, ...depths);
          }
          break;
        case ColumnClass.CoreRow:
          if (info?.data?.length > 0) {
            const depths = info.data
              .map((item: any) => [
                Number(item.depthFrom),
                Number(item.depthTo),
              ])
              .flat();
            minDepth = Math.min(minDepth, ...depths);
            maxDepth = Math.max(maxDepth, ...depths);
            break;
          }
        case ColumnClass.Geology:
          if (info?.data?.length > 0) {
            const depths = info.data
              .map((item: any) => [
                Number(item.depthFrom),
                Number(item.depthTo),
              ])
              .flat();
            minDepth = Math.min(minDepth, ...depths);
            maxDepth = Math.max(maxDepth, ...depths);
            break;
          }
      }
    });

    if (minDepth === Infinity || maxDepth === -Infinity) {
      return { maxHeight: 0, startY: 0 };
    }

    const columnStartY = minDepth * CONFIG.SCALE;
    const columnHeight = (maxDepth - minDepth) * CONFIG.SCALE + 150;

    return { maxHeight: columnHeight, startY: columnStartY };
  }, [result]);

  console.log("maxHeight: ", maxHeight);

  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );

  useEffect(() => {
    requestGetListLoggingView({
      isActive: true,
      maxResultCount: 1000,
      skipCount: 0,
      projectId: globalProjectId?.toString(),
    });
  }, [globalProjectId]);

  useEffect(() => {
    if (loggingViewIdParams) {
      onSelectLoggingView(loggingViewIdParams);
    }
  }, [drillholeDetailId]);

  const handleTouchStart = useCallback(
    (e: any) => {
      e.evt.preventDefault();
      const touches = e.evt.touches;
      touchStartTime.current = Date.now();

      if (touches.length === 2) {
        setIsZooming(true);
        const stage = stageRef.current;
        if (!stage) return;

        const p1 = { x: touches[0].clientX, y: touches[0].clientY };
        const p2 = { x: touches[1].clientX, y: touches[1].clientY };

        const center = getCenter(p1, p2);
        const dist = getDistance(p1, p2);

        const stageBox = stage.container().getBoundingClientRect();
        const stagePoint = {
          x: center.x - stageBox.left,
          y: center.y - stageBox.top,
        };

        const oldScale = stage.scaleX();
        const centerPointTo = {
          x: (stagePoint.x - stage.x()) / oldScale,
          y: (stagePoint.y - stage.y()) / oldScale,
        };

        initialTouchCenterRef.current = centerPointTo;
        setLastCenter(center);
        setLastDist(dist);
      } else if (touches.length === 1) {
        setIsZooming(false);
      }
    },
    [getCenter, getDistance]
  );

  const handleTouchMove = useCallback(
    (e: any) => {
      e.evt.preventDefault();
      const touches = e.evt.touches;
      if (touches.length !== 2) return;

      const stage = stageRef.current;
      if (!stage || !lastCenter || !lastDist || !initialTouchCenterRef.current)
        return;

      const p1 = { x: touches[0].clientX, y: touches[0].clientY };
      const p2 = { x: touches[1].clientX, y: touches[1].clientY };

      const dist = getDistance(p1, p2);
      const currentCenter = getCenter(p1, p2);
      const stageBox = stage.container().getBoundingClientRect();

      const stagePoint = {
        x: currentCenter.x - stageBox.left,
        y: currentCenter.y - stageBox.top,
      };

      const scaleFactor = Math.pow(dist / lastDist, 1.2);
      const zoomSpeed = 1.03;
      const currentScale = stage.scaleX();
      const targetScale = Math.min(
        Math.max(
          currentScale * scaleFactor * zoomSpeed,
          CONFIG.MIN_SCALE || 0.1
        ),
        CONFIG.MAX_SCALE || 10
      );

      stage.scale({ x: targetScale, y: targetScale });

      const newPos = {
        x: stagePoint.x - initialTouchCenterRef.current.x * targetScale,
        y: stagePoint.y - initialTouchCenterRef.current.y * targetScale,
      };

      stage.position(newPos);
      stage.batchDraw();

      setLastDist(dist);
      setLastCenter(currentCenter);
      setIsZooming(true);
    },
    [lastCenter, lastDist, getDistance, getCenter]
  );

  const handleTouchEnd = useCallback((e: any) => {
    e.evt.preventDefault();

    if (e.evt.touches.length < 2) {
      initialTouchCenterRef.current = null;
      setLastCenter(null);
      setLastDist(0);
      setIsZooming(false);
    }

    if (e.evt.touches.length === 0) {
      setIsZooming(false);
      touchStartTime.current = 0;
    }
  }, []);
  useEffect(() => {
    if (depthInputParams) {
      if (depthInputParams && !isNaN(Number(depthInputParams))) {
        const stage = stageRef.current;
        if (!stage) return;

        // Get the entered depth
        const depth = Number(depthInputParams);
        console.log("Going to depth:", depthInputParams);

        // Save the target depth for scale change handling
        setTargetDepth(depth);

        // Calculate the Y position based on the depth
        const offsetY = depth * CONFIG.SCALE - 200;
        console.log("Setting offsetY to:", offsetY);

        // Update both the offsetY and targetDepth in Redux
        dispatch(
          updateOffsetY({
            offsetY: offsetY,
            targetDepth: depth,
          })
        );

        // Reset stage position to ensure offsetY works correctly
        stage.position({ x: stage.x(), y: 0 });
        stage.batchDraw();
      }
    }
  }, [depthInputParams, dispatch, router, params]);
  // Handle going to a specific depth
  const handleGoToDepth = () => {
    if (depthInput && !isNaN(Number(depthInput))) {
      const stage = stageRef.current;
      if (!stage) return;

      // Get the entered depth
      const depth = Number(depthInput);
      console.log("Going to depth:", depth);

      // Save the target depth for scale change handling
      setTargetDepth(depth);

      // Calculate the Y position based on the depth
      const offsetY = depth * CONFIG.SCALE - 200;
      console.log("Setting offsetY to:", offsetY);

      // Update both the offsetY and targetDepth in Redux
      dispatch(
        updateOffsetY({
          offsetY: offsetY,
          targetDepth: depth,
        })
      );

      // Update URL params with depthInput
      params.set("depthInput", depthInput);
      router.push(`${window.location.pathname}?${params.toString()}`);

      // Reset stage position to ensure offsetY works correctly
      stage.position({ x: stage.x(), y: 0 });
      stage.batchDraw();
    }
  };

  // Update offsetY when scale changes if we have a target depth
  const handleScaleChange = () => {
    const stage = stageRef.current;
    if (!stage || targetDepth === null) return;

    const currentScale = stage.scaleX();
    console.log("Scale changed to:", currentScale);

    // Recalculate offsetY with the same target depth
    const offsetY = targetDepth * CONFIG.SCALE - 200;
    console.log("Recalculating offsetY to:", offsetY);

    // Update both the offsetY and keep the same targetDepth in Redux
    dispatch(
      updateOffsetY({
        offsetY: offsetY,
        targetDepth: targetDepth,
      })
    );

    // REMOVED: stage.position({ x: stage.x(), y: 0 });
    // The stage's y position should be preserved after panning or wheel zooming.
    // The offsetY prop, combined with the current stage.y(), will determine the view.
    stage.batchDraw();
  };

  return (
    <div className="w-full h-full flex-1">
      <div className="w-full flex justify-between items-center my-2">
        <div className="flex gap-2 items-center">
          <p className="text-sm font-bold">Logging View</p>
          <Select
            options={loggingViews?.map((item: any) => ({
              label: item.name,
              value: item.id.toString(),
            }))}
            placeholder="Select logging view"
            className="w-[200px]"
            onChange={onSelectLoggingView}
            value={loggingViewIdParams}
          />
        </div>
        <div className="flex items-center">
          <div className="flex items-center mr-2">
            <span className="text-sm font-medium mr-2">Depth</span>
            <Space.Compact className="flex items-center">
              <InputNumber
                size="large"
                value={depthInputParams ? Number(depthInputParams) : undefined}
                onChange={(value) => setDepthInput(value?.toString() || "")}
                placeholder="Enter depth"
                style={{ width: 120 }}
                min={0}
                onPressEnter={handleGoToDepth}
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    handleGoToDepth();
                  }
                }}
              />
              <Button
                size="large"
                onClick={handleGoToDepth}
                icon={<SearchOutlined />}
              >
                Go
              </Button>
            </Space.Compact>
          </div>

          <Divider type="vertical" className="h-8 mx-2" />

          {loggingViewIdParams && (
            <Button
              size="large"
              style={{ width: "100px" }}
              icon={
                <div className="flex items-center gap-1">
                  Refresh
                  <ReloadOutlined />
                </div>
              }
              onClick={() =>
                loggingViewIdParams && onSelectLoggingView(loggingViewIdParams)
              }
            />
          )}

          <Popover
            title="Geophysic Column Config"
            placement="bottomLeft"
            arrow
            content={
              <ColumnSettings
                displayColumn={displayColumn}
                setDisplayColumn={(newDisplayColumn) =>
                  dispatch(updateDisplayColumn(newDisplayColumn))
                }
              />
            }
          >
            <Button size="large" icon={<SettingOutlined />} />
          </Popover>
        </div>
      </div>

      {isLoading ? (
        <div className="w-full h-full flex items-center justify-center">
          <Spin size="large" />
        </div>
      ) : (
        <AutoSizerTmp>
          {({ width, height }) => (
            <Stage
              ref={stageRef}
              width={width}
              height={screenHeight}
              draggable={!isZooming}
              onWheel={handleOnWheelStage}
              onTouchStart={handleTouchStart}
              onTouchMove={handleTouchMove}
              onTouchEnd={handleTouchEnd}
              offsetY={
                currentOffsetY !== undefined
                  ? currentOffsetY
                  : result[0]?.data?.length && result[0]?.data[0]?.depthFrom
                    ? result[0]?.data[0]?.depthFrom * CONFIG.SCALE - 200
                    : 0
              }
              onTransformEnd={() => {
                // Handle scale changes
                if (stageRef.current) {
                  handleScaleChange();
                }
              }}
            >
              <Layer ref={layerRef}>
                {result.map((info, index) => {
                  switch (info?.columnClass) {
                    case ColumnClass.Geophysics:
                      return (
                        <Group key={index}>
                          {info?.data?.length > 0 && (
                            <>
                              <Rect
                                x={info?.startX - CONFIG.DEFAULT_PADDING}
                                y={startY - CONFIG.DEFAULT_PADDING * 2}
                                width={info?.width + CONFIG.DEFAULT_PADDING * 2}
                                height={maxHeight}
                                stroke="#000"
                                strokeWidth={2}
                              />
                              <Text
                                text={info?.attribute?.name}
                                x={info?.startX - CONFIG.DEFAULT_PADDING + 10}
                                y={startY - 50}
                                fontStyle="bold"
                                {...CONFIG.TEXT_STYLES.TITLE}
                              />
                            </>
                          )}
                          <GeophysicsData
                            info={info}
                            displayColumn={displayColumn}
                          />
                        </Group>
                      );
                    case ColumnClass.Assay:
                      const sortedData = info.data?.sort(
                        (a: any, b: any) =>
                          Number(a["Depth From"]) - Number(b["Depth From"])
                      );

                      if (!sortedData?.length) return null;

                      return (
                        <Group key={index}>
                          <Rect
                            x={info?.startX - CONFIG.DEFAULT_PADDING}
                            y={startY - CONFIG.DEFAULT_PADDING * 2}
                            width={info?.width + CONFIG.DEFAULT_PADDING * 2}
                            height={maxHeight}
                            stroke="#000"
                            strokeWidth={2}
                          />
                          <Text
                            text={info?.assayAttribute?.name}
                            x={info?.startX - CONFIG.DEFAULT_PADDING + 10}
                            y={startY - 50}
                            fontStyle="bold"
                            {...CONFIG.TEXT_STYLES.TITLE}
                          />
                          {sortedData?.map((item: any, itemIndex: number) => (
                            <Group key={`${index}-${itemIndex}`}>
                              <AssayData
                                info={{
                                  ...info,
                                  assayAttribute: {
                                    ...info.assayAttribute,
                                    backgroundColor:
                                      info?.assayAttribute?.backgroundColor ||
                                      "#b3d9ff",
                                  },
                                }}
                                item={item}
                                displayColumn={displayColumn}
                              />
                            </Group>
                          ))}
                        </Group>
                      );
                    case ColumnClass.CoreRow:
                      if (!info?.data?.length) return null;

                      const sortedCoreRowData = info.data.sort(
                        (a: any, b: any) => a.depthFrom - b.depthFrom
                      );
                      const paddingCoreRow = CONFIG.DEFAULT_PADDING - 40;
                      return (
                        <Group key={index}>
                          <Rect
                            x={info?.startX - paddingCoreRow}
                            y={startY - CONFIG.DEFAULT_PADDING * 2}
                            width={info?.width + paddingCoreRow * 2 + 40}
                            height={maxHeight}
                            stroke="#000"
                            strokeWidth={2}
                          />
                          <Text
                            text={`${info?.name}`}
                            x={info?.startX - paddingCoreRow + 10}
                            y={startY - 50}
                            fontStyle="bold"
                            {...CONFIG.TEXT_STYLES.TITLE}
                          />
                          {sortedCoreRowData.map(
                            (image: any, imageIndex: number) => {
                              // Calculate height based on depth range
                              const depthFrom = image.depthFrom || 0;
                              const depthTo = image.depthTo || 0;
                              const heightScale = CONFIG.SCALE;

                              return (
                                <Group
                                  key={`core-row-${index}-${imageIndex}`}
                                  x={info.startX + info.width / 2 + 40}
                                  y={depthFrom * heightScale}
                                >
                                  {/* Render the actual image */}
                                  <ZoomableImage
                                    width={image.height * heightScale}
                                    height={image.width * heightScale * 10}
                                    x={0}
                                    rotation={90} // Rotate 90 degrees for vertical orientation
                                    src={
                                      image.urlCroppedImage || image.mediumSize
                                    }
                                    crossOrigin="Anonymous"
                                    onClick={() => {
                                      setPreviewUrl(
                                        image.urlCroppedImage ||
                                        image.mediumSize
                                      );
                                    }}
                                  />

                                  {/* Depth markers */}
                                  <Text
                                    text={`- ${depthFrom.toFixed(2)}`}
                                    x={0}
                                    y={-6}
                                    fontSize={12}
                                    fill="black"
                                  />

                                  <Text
                                    text={`- ${depthTo.toFixed(2)}`}
                                    x={0}
                                    y={image.height * heightScale - 6}
                                    fontSize={12}
                                    fill="black"
                                  />
                                </Group>
                              );
                            }
                          )}
                        </Group>
                      );
                    case ColumnClass.Geology:
                      const geologySuiteFieldId = info?.geologySuiteField?.id;
                      const newData = info.data
                        ?.filter((item: any) => item.depthFrom <= item.depthTo)
                        ?.map((item: any) => ({
                          ...item,
                          dataEntryValues: item?.dataEntryValues?.filter(
                            (dataEntryValue: any) =>
                              dataEntryValue?.geologysuiteFieldId ===
                              geologySuiteFieldId
                          ),
                        }))
                        .sort((a: any, b: any) => a.depthFrom - b.depthFrom);

                      const firstItem = newData?.[0];
                      const lastItem = newData?.[newData.length - 1];
                      if (!firstItem || !lastItem) return null;
                      return (
                        <Group key={index}>
                          <Rect
                            x={info?.startX - CONFIG.DEFAULT_PADDING}
                            y={startY - CONFIG.DEFAULT_PADDING * 2}
                            width={info?.width + CONFIG.DEFAULT_PADDING * 2}
                            height={maxHeight}
                            stroke="#000"
                            strokeWidth={2}
                          />
                          {/* <Text
                            text={info?.attribute?.name}
                            x={info?.startX}
                            y={startY - 50}
                            width={info?.width - 10}
                            fontStyle="bold"
                            {...CONFIG.TEXT_STYLES.TITLE}
                          /> */}
                          <Text
                            text={info?.geologySuiteField?.name}
                            x={info?.startX - CONFIG.DEFAULT_PADDING + 10}
                            y={startY - 50}
                            fontStyle="bold"
                            {...CONFIG.TEXT_STYLES.TITLE}
                          />
                          {newData?.map((item: any, itemIndex: number) => (
                            <Group key={`${index}-${itemIndex}`}>
                              <GeologyData
                                info={info}
                                item={item}
                                displayColumn={displayColumn}
                              />
                            </Group>
                          ))}
                        </Group>
                      );
                    default:
                      return null;
                  }
                })}
              </Layer>
            </Stage>
          )}
        </AutoSizerTmp>
      )}

      {previewUrl && (
        <Image
          src={previewUrl}
          preview={{
            visible: !!previewUrl,
            onVisibleChange: () => setPreviewUrl(""),
          }}
          style={{
            display: "none",
          }}
        />
      )}
    </div>
  );
}
