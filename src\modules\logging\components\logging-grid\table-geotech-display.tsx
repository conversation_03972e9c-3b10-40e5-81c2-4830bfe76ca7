import React, { memo, useMemo } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { StructureSelectorType } from "@/modules/structure-type/enum/enum";
import { AutoSizer, List, ListRowRenderer } from "react-virtualized";
import {
  VirtualizedTableHeader,
  VirtualizedTableRow,
} from "../render-field-types";
import { AiOutlineFontSize } from "react-icons/ai";
import { ImFontSize } from "react-icons/im";

// Type-safe wrappers for react-virtualized components
const AutoSizerComponent: any = AutoSizer;
const ListComponent: any = List;

interface TableGeotechDisplayProps {
  searchText: string;
  fontSize: number;
  dynamicWidth: number;
  handleFontSizeChange: (increment: boolean) => void;
}

export const TableGeotechDisplay = memo<TableGeotechDisplayProps>(
  ({ searchText, fontSize, dynamicWidth, handleFontSizeChange }) => {
    // Redux state
    const geotechData = useAppSelector((state) => state.logging.geotechData);
    const structure = useAppSelector((state) => state.structure.detail);

    // Determine if it's interval or point based structure
    const isInterval = structure?.selector === StructureSelectorType.Interval;
    const isPoint = structure?.selector === StructureSelectorType.Points;

    // Define columns based on structure type
    const columns = useMemo(() => {
      if (isInterval) {
        return [
          {
            title: "Depth From",
            key: "depth",
            width: 120,
            dataIndex: "depth",
            render: (value: any) => (
              <div className="p-2 text-center">
                {value?.toFixed(2) || "N/A"}
              </div>
            ),
          },
          {
            title: "Depth To",
            key: "depthTo",
            width: 120,
            dataIndex: "depthTo",
            render: (value: any) => (
              <div className="p-2 text-center">
                {value?.toFixed(2) || "N/A"}
              </div>
            ),
          },
          {
            title: "Alpha Angle",
            key: "alphaAngle",
            width: 120,
            dataIndex: "alphaAngle",
            render: (value: any) => (
              <div className="p-2 text-center">
                {value?.toFixed(2) || "N/A"}
              </div>
            ),
          },
          {
            title: "Beta Angle",
            key: "betaAngle",
            width: 120,
            dataIndex: "betaAngle",
            render: (value: any) => (
              <div className="p-2 text-center">
                {value?.toFixed(2) || "N/A"}
              </div>
            ),
          },
          {
            title: "Rock Type",
            key: "rockType",
            width: 150,
            dataIndex: "rockType",
            render: (_: any, record: any) => (
              <div className="p-2">{record?.rockType?.name || "N/A"}</div>
            ),
          },
          {
            title: "Width",
            key: "width",
            width: 100,
            dataIndex: "width",
            render: (value: any) => (
              <div className="p-2 text-center">
                {value?.toFixed(2) || "N/A"}
              </div>
            ),
          },
        ];
      }

      // Point structure columns
      return [
        {
          title: "Depth",
          key: "depth",
          width: 120,
          dataIndex: "depth",
          render: (value: any) => (
            <div className="p-2 text-center">{value?.toFixed(2) || "N/A"}</div>
          ),
        },
        {
          title: "Alpha Angle",
          key: "alphaAngle",
          width: 120,
          dataIndex: "alphaAngle",
          render: (value: any) => (
            <div className="p-2 text-center">{value?.toFixed(2) || "N/A"}</div>
          ),
        },
        {
          title: "Beta Angle",
          key: "betaAngle",
          width: 120,
          dataIndex: "betaAngle",
          render: (value: any) => (
            <div className="p-2 text-center">{value?.toFixed(2) || "N/A"}</div>
          ),
        },
        {
          title: "Rock Type",
          key: "rockType",
          width: 150,
          dataIndex: "rockType",
          render: (_: any, record: any) => (
            <div className="p-2">{record?.rockType?.name || "N/A"}</div>
          ),
        },
        {
          title: "Width",
          key: "width",
          width: 100,
          dataIndex: "width",
          render: (value: any) => (
            <div className="p-2 text-center">{value?.toFixed(2) || "N/A"}</div>
          ),
        },
      ];
    }, [isInterval, isPoint]);

    // Filter data based on search text
    const filteredData = useMemo(() => {
      if (!searchText) return geotechData || [];

      return (geotechData || []).filter((item: any) => {
        const searchLower = searchText.toLowerCase();
        return (
          item.depth?.toString().includes(searchLower) ||
          item.depthTo?.toString().includes(searchLower) ||
          item.alphaAngle?.toString().includes(searchLower) ||
          item.betaAngle?.toString().includes(searchLower) ||
          item.width?.toString().includes(searchLower) ||
          item.rockGroup?.name?.toLowerCase().includes(searchLower)
        );
      });
    }, [geotechData, searchText]);

    // Calculate total table width
    const totalTableWidth = useMemo(() => {
      return columns.reduce((sum, column) => sum + (column.width || 150), 0);
    }, [columns]);

    // Fixed row height for virtualized table
    const FIXED_ROW_HEIGHT = 60;

    return (
      <div className="bg-white border border-gray-200 rounded-lg shadow-sm flex-1 min-h-0 overflow-auto">
        <div className="h-full flex flex-col">
          {/* Geotech Controls */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
            <div className="text-sm text-gray-600">
              {filteredData.length} geotech record(s) | Read-only view
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-600">Font:</span>
              <Tooltip title="Decrease font size">
                <Button
                  size="small"
                  onClick={() => handleFontSizeChange(false)}
                  disabled={fontSize <= 10}
                  icon={<AiOutlineFontSize style={{ fontSize: "12px" }} />}
                />
              </Tooltip>
              <span className="text-xs text-gray-500 px-2">{fontSize}px</span>
              <Tooltip title="Increase font size">
                <Button
                  size="small"
                  onClick={() => handleFontSizeChange(true)}
                  disabled={fontSize >= 20}
                  icon={<ImFontSize style={{ fontSize: "14px" }} />}
                />
              </Tooltip>
            </div>
          </div>

          {/* Table Header */}
          <div
            className="virtualized-table-header flex-shrink-0"
            style={{
              minWidth: totalTableWidth,
            }}
          >
            <VirtualizedTableHeader
              columns={columns}
              fontSize={fontSize}
              totalWidth={totalTableWidth}
            />
          </div>

          {/* Virtualized Table Body */}
          <div
            className="flex-1"
            style={{
              height: "100%",
              minHeight: 0,
              minWidth: totalTableWidth,
            }}
          >
            {filteredData.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-500">
                No geotech data available
              </div>
            ) : (
              <AutoSizerComponent>
                {({ width, height }) => {
                  const actualHeight = height || 0;
                  const actualWidth = width || 0;

                  if (actualHeight < 50) {
                    return (
                      <div className="text-center p-4 text-red-500">
                        Container too small for virtualization (height:{" "}
                        {actualHeight}px)
                      </div>
                    );
                  }

                  const rowRenderer: ListRowRenderer = ({
                    index,
                    key,
                    style,
                  }) => {
                    const record = filteredData[index];

                    if (!record) {
                      return (
                        <div
                          key={key}
                          style={style}
                          className="flex items-center justify-center text-red-500 border border-red-300"
                        >
                          No record at index {index} (Total:{" "}
                          {filteredData.length})
                        </div>
                      );
                    }

                    return (
                      <VirtualizedTableRow
                        key={key}
                        record={record}
                        index={index}
                        columns={columns}
                        style={style}
                        fontSize={fontSize}
                        totalWidth={totalTableWidth}
                      />
                    );
                  };

                  return (
                    <ListComponent
                      height={actualHeight}
                      width={Math.max(actualWidth, totalTableWidth)}
                      rowCount={filteredData.length}
                      rowHeight={FIXED_ROW_HEIGHT}
                      rowRenderer={rowRenderer}
                      overscanRowCount={10}
                      className="high-performance-table-body"
                      style={{ minWidth: totalTableWidth }}
                    />
                  );
                }}
              </AutoSizerComponent>
            )}
          </div>
        </div>
      </div>
    );
  }
);

TableGeotechDisplay.displayName = "TableGeotechDisplay";
