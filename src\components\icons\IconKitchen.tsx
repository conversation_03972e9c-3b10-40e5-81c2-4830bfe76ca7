import React from "react";

const IconKitchen = () => {
  return (
    <svg
      width="18"
      height="18"
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.89021 3.00052C7.7097 3.01864 7.57224 3.17062 7.57244 3.35212V4.16153L6.33233 6.23396C6.26694 6.34328 6.26547 6.4794 6.32859 6.5901C6.39171 6.7008 6.50959 6.76896 6.63701 6.76827H11.3631C11.4902 6.76817 11.6074 6.69982 11.67 6.58921C11.7326 6.4787 11.7311 6.34298 11.6658 6.23395L10.4257 4.16152V3.35212C10.4249 3.15721 10.2662 2.99971 10.0713 3.00052H7.92681C7.9146 2.99983 7.90248 2.99983 7.89027 3.00052L7.89021 3.00052ZM8.27832 3.70375H9.71903V3.90231H8.27832V3.70375ZM8.12736 4.60829H9.87068L10.7379 6.0623H7.25736L8.12736 4.60829ZM3.35156 8.66767C3.15667 8.66846 2.9992 8.82712 3 9.02204V14.6477C3.0004 14.8418 3.15747 14.9992 3.35156 15H14.6457C14.7394 15.0004 14.8294 14.9634 14.8959 14.8973C14.9624 14.8312 14.9998 14.7415 15 14.6477V9.02205C15.0004 8.928 14.9632 8.83758 14.8966 8.7711C14.8301 8.70452 14.7397 8.66729 14.6457 8.66769L3.35156 8.66767ZM3.70589 9.37433H6.76437V11.4819H3.70589V9.37433ZM7.47101 9.37433H10.5295V10.5105H7.47101V9.37433ZM11.2354 9.37433H14.2939V14.2935H11.2354V9.37433ZM4.28698 9.65011C4.09189 9.66026 3.9419 9.82661 3.95196 10.0217C3.9621 10.2168 4.12843 10.3668 4.32351 10.3568H4.87843C5.07351 10.3568 5.23168 10.1986 5.23168 10.0034C5.23168 9.80827 5.07352 9.65009 4.87843 9.65009H4.32351C4.3113 9.6494 4.29918 9.6494 4.28697 9.65009L4.28698 9.65011ZM11.8271 9.65011C11.632 9.66026 11.482 9.82661 11.492 10.0217C11.5022 10.2168 11.6685 10.3668 11.8636 10.3568H12.4185C12.6136 10.3568 12.7718 10.1986 12.7718 10.0034C12.7718 9.80827 12.6136 9.65009 12.4185 9.65009H11.8636C11.8514 9.6494 11.8393 9.6494 11.8271 9.65009L11.8271 9.65011ZM7.47101 11.2165H10.5295V14.2936H7.47101V11.2165ZM8.06108 11.4792C8.06059 11.4794 8.06019 11.4797 8.0597 11.4799C7.87919 11.498 7.74173 11.65 7.74193 11.8315V13.6785C7.74153 13.7725 7.77876 13.8629 7.84533 13.9294C7.9118 13.996 8.0022 14.0332 8.09625 14.0328H9.90162C9.99566 14.0332 10.0861 13.996 10.1525 13.9294C10.2191 13.8629 10.2563 13.7725 10.2559 13.6785V11.8315C10.2551 11.6366 10.0965 11.4791 9.90161 11.4799H8.09624C8.08452 11.4791 8.0728 11.4788 8.06108 11.4792H8.06108ZM8.4478 12.1831H9.55006V13.3269H8.4478V12.1831ZM3.70578 12.1886H6.76427V14.2934L3.70578 14.2933V12.1886ZM4.30551 12.4361V12.436C4.21058 12.4339 4.1188 12.4702 4.05084 12.5367C3.98298 12.6031 3.94468 12.6941 3.94468 12.789C3.94468 12.8841 3.98298 12.9751 4.05084 13.0414C4.11878 13.1079 4.21056 13.1442 4.30551 13.142H4.86043C4.95536 13.1442 5.04714 13.1079 5.1151 13.0414C5.18295 12.9751 5.22126 12.8841 5.22126 12.789C5.22126 12.6941 5.18295 12.6031 5.1151 12.5367C5.04716 12.4702 4.95538 12.4339 4.86043 12.436L4.30551 12.4361Z"
        fill="#808191"
      />
    </svg>
  );
};

export default IconKitchen;
