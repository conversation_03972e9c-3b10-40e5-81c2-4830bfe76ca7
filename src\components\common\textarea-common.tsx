"use client";
import { Input } from "antd";
import TextArea from "antd/es/input/TextArea";
import { ChangeEventHandler } from "react";
import { FormItem } from "react-hook-form-antd";

export interface ITextAreaCommonProps {
  label?: string;
  name: string;
  placeholder?: string;
  prefix?: React.ReactNode;
  control?: any;
  size?: "large" | "middle" | "small";
  disabled?: boolean;
  rows?: number;
  maxLength?: number;
  onChange?: ChangeEventHandler<HTMLTextAreaElement>;
  isRequired?: boolean;
  description?: any;
  autoSize?: boolean;
}

export function TextAreaCommon(props: ITextAreaCommonProps) {
  const {
    label,
    name,
    placeholder,
    prefix,
    control,
    size = "middle",
    disabled = false,
    rows = 4,
    maxLength = 500,
    onChange,
    isRequired,
    description,
    autoSize = false,
  } = props;
  return (
    <div className={"flex flex-col gap-2"}>
      <p className="font-medium">
        {label}{" "}
        {isRequired ? (
          <span className="text-14-16 md:text-16-20 text-error">*</span>
        ) : null}
      </p>
      {description && <>{description}</>}
      <FormItem control={control} name={name}>
        <TextArea
          style={{
            fontFamily: "Visby",
            maxHeight: "140px",
          }}
          name={name}
          placeholder={placeholder}
          size={size}
          disabled={disabled}
          rows={rows}
          maxLength={maxLength}
          onChange={onChange}
          autoSize={autoSize}
        />
      </FormItem>
    </div>
  );
}
