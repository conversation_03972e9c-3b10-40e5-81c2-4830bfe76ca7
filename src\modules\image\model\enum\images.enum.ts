import { DrillholeEnum } from "../../../drillhole/model/enum/drillhole.enum";

export enum ImageView {
  List = "List",
  DrillHole = "Drill Hole",
}

export const imageTypeOptions = [
  {
    label: "Drilling",
    value: 1,
  },
  {
    label: "Map",
    value: 2,
  },
  {
    label: "General",
    value: 3,
  },
];
export const imageCategoryOptions = [
  {
    label: "Drilling",
    value: 1,
  },
  {
    label: "Map",
    value: 2,
  },
  {
    label: "General",
    value: 3,
  },
];

export const ImageStatusOptions = [
  {
    label: "Not Started",
    value: DrillholeEnum.NotStarted,
  },
  {
    label: "In Progress",
    value: DrillholeEnum.InProgress,
  },
  {
    label: "Reprocess",
    value: DrillholeEnum.ReProcess,
  },
  {
    label: "Review",
    value: DrillholeEnum.Review,
  },
  {
    label: "Complete",
    value: DrillholeEnum.Complete,
  },
];

export const ProcessImageViewOptions = [
  { label: "Rows", value: "row" },
  { label: "Box", value: "box" },
];

export enum ImageSizeEnum {
  FULL_SIZE = "full_size",
  LARGE = "large",
  MEDIUM = "medium",
  THUMBNAIL = "thumbnail",
  SMALL = "small",
}

export const ImageSizeOptions = [
  { label: "Full Size", value: ImageSizeEnum.FULL_SIZE },
  { label: "Medium", value: ImageSizeEnum.MEDIUM },
];
export enum ImageCategory {
  Drilling = 1,
  Map,
  General,
}

export enum Type {
  Standard = 1,
  Hyperspectral,
  Optical,
  GeologyData,
  Rig,
}

export enum ImageStatus {
  NotStarted = 1,
  InProgress,
  Reprocess,
  Review,
  Complete,
  Unnamed,
}

export enum StandardType {
  Dry = 1,
  Wet,
}
