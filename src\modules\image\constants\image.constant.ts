export const imageTypeOptions = [
  { label: "Drilling", value: 1 },
  { label: "Map", value: 2 },
  { label: "General", value: 3 },
];

export const categoryOptions = [
  { label: "Standard", value: 1 },
  { label: "Hyperspectral", value: 2 },
  { label: "Optical", value: 3 },
  { label: "Geology Data", value: 4 },
  { label: "Rig", value: 5 },
];
export enum CategoryOptionsEnum {
  Standard = 1,
  Hyperspectral = 2,
  Optical = 3,
  GeologyData = 4,
  Rig = 5,
}
export const categoryOptionsExport = [
  { label: "Standard", value: 1 },
  { label: "Hyperspectral", value: 2 },
  { label: "Optical", value: 3 },
  { label: "Geology Data", value: 4 },
  { label: "Rig", value: 5 },
];
export const categoryOptionExport = [
  { label: "Image", value: 1 },
  { label: "Geophysics Data", value: 2 },
];

export const imageCategoryOptions = [
  { label: "Drilling", value: 1 },
  { label: "Map", value: 2 },
  { label: "General", value: 3 },
];

export const standardTypeOptions = [
  { label: "Wet", value: 2 },
  { label: "Dry", value: 1 },
];

export const standardTypeOptionsExport = [
  { label: "All", value: 0 },
  { label: "Dry", value: 1 },

  { label: "Wet", value: 2 },
];
export const imageSizeOptions = [
  { label: "FullSize", value: 1 },
  { label: "Medium", value: 2 },
];

export const ImageStatus = [
  { label: "Not Started", value: "1" },
  { label: "In Progress", value: "2" },
  { label: "Reprocess", value: "3" },
  { label: "Review", value: "4" },
  { label: "Complete", value: "5" },
  { label: "Unnamed", value: "6" },
];
