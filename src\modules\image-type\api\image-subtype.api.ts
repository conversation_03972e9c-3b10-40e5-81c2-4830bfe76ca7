import { RequestState } from "@/common/configs/app.contants";
import { appRequest } from "@/common/configs/app.di-container";
import { getErrorMessage } from "@/utils/error.utils";
import { ImageSubTypeQuery } from "../interface/image-type.query";
import { ImageSubTypeBodyType } from "../model/schema/image-type.schema";

const imageSubTypeRequest = {
  getList: async (params: ImageSubTypeQuery) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImageSubtype/GetAll`,
        params
      );
      return {
        state: RequestState.success,
        data: {
          items: response?.result?.items,
          pagination: {
            current:
              Math.floor(
                (params?.skipCount ?? 1) / (params?.maxResultCount ?? 10)
              ) + 1,
            pageSize: params?.maxResultCount ?? 10,
            total: response?.result?.totalCount,
          },
        },
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
  getDetail: async (id: string) => {
    try {
      const response = await appRequest.get<any>(
        `/services/app/ImageSubtype/Get?Id=${id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  create: async (body: ImageSubTypeBodyType) => {
    try {
      const response = await appRequest.post<any>(
        `/services/app/ImageSubtype/Create`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  delete: async (params: { id: string }) => {
    try {
      const response = await appRequest.delete<any>(
        `/services/app/ImageSubtype/Delete?Id=${params.id}`
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },

  update: async (body: ImageSubTypeBodyType) => {
    try {
      const response = await appRequest.put<any>(
        `/services/app/ImageSubtype/Update`,
        body
      );
      return {
        state: RequestState.success,
        data: response?.result,
      };
    } catch (error) {
      return {
        state: RequestState.error,
        message: getErrorMessage(error),
      };
    }
  },
};

export default imageSubTypeRequest;
