import { createAppAsyncThunk } from "@/common/vendors/redux/store/createAppAsyncThunk";
import imageSubTypeRequest from "../api/image-subtype.api";
import imageTypeRequest from "../api/image-type.api";
import {
  ImageSubTypeQuery,
  ImageTypeQuery,
} from "../interface/image-type.query";

export const getListImageType = createAppAsyncThunk(
  "imageType/list",
  async (query: ImageTypeQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await imageTypeRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });

    return response;
  }
);

export const getDetailImageType = createAppAsyncThunk(
  "imageType/detail",
  async (id: string) => {
    const response = await imageTypeRequest.getDetail(id);
    return response.data;
  }
);
export const getListImageSubType = createAppAsyncThunk(
  "imageSubType/list",
  async (query: ImageSubTypeQuery) => {
    const { page = 1, pageSize = 10, ...otherQueries } = query;
    const response = await imageSubTypeRequest.getList({
      skipCount: (page - 1) * pageSize,
      maxResultCount: pageSize,
      ...otherQueries,
    });

    return response;
  }
);

export const getDetailImageSubType = createAppAsyncThunk(
  "imageSubType/detail",
  async (id: string) => {
    const response = await imageSubTypeRequest.getDetail(id);
    return response.data;
  }
);
