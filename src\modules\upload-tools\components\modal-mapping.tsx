import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ModalCommon } from "@/components/common/modal-common";
import { useQueryAssaySuite } from "@/modules/assay/hooks/useQueryAssaySuite";
import { useQuerySuite } from "@/modules/downhole-point/hooks/useQuerySuite";
import { useQueryDownholeSurveyType } from "@/modules/downhole-survey-type/hooks/useQueryDownholeSurveyType";
import { useQueryGeologySuite } from "@/modules/geology-suite/hooks/useQueryGeologySuite";
import { Select } from "antd";
import { useEffect } from "react";
import {
  ImportMappingType,
  ImportMappingTypeOptions,
} from "../api/importData.api";
import { usePreviewMappingFields } from "../hooks/usePreviewMappingFields";
import { TemplateItem } from "./template-item";
import geologySuiteRequest from "@/modules/geology-suite/api/geology-suite.api";
import { setGeologySuite } from "../redux/importDataSlice/import-data.slice";

export const ModalMapping = ({
  openModalMapping,
  setOpenModalMapping,
  setValue,
  refetchTemplate,
  watch,
}: {
  openModalMapping: boolean;
  setOpenModalMapping: (open: boolean) => void;
  setValue: any;
  refetchTemplate: any;
  watch: any;
}) => {
  const suiteId = watch("suiteId");
  const importFileType = watch("ImportFileType");
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const {
    data: geologySuiteData,
    setSearchParams: geologySuiteSetSearchParams,
    searchParams: geologySuiteSearchParams,
    setEnable: geologySuiteSetEnable,
  } = useQueryGeologySuite();
  const {
    data: geophysicsSuiteData,
    setSearchParams: geophysicsSuiteSetSearchParams,
    searchParams: geophysicsSuiteSearchParams,
    setEnable: geophysicsSuiteSetEnable,
  } = useQuerySuite();
  const {
    data: assaySuiteData,
    setSearchParams: assaySuiteSetSearchParams,
    searchParams: assaySuiteSearchParams,
    setEnable: assaySuiteSetEnable,
  } = useQueryAssaySuite();
  const {
    data: downholeSurveyTypeData,
    setSearchParams: downholeSurveyTypeSetSearchParams,
    searchParams: downholeSurveyTypeSearchParams,
    setEnable: downholeSurveyTypeSetEnable,
  } = useQueryDownholeSurveyType();

  const { request: requestPreviewMappingFields, data: previewMappingFields } =
    usePreviewMappingFields();

  useEffect(() => {
    switch (importFileType) {
      case ImportMappingType.Geology:
        geologySuiteSetEnable(true);
        geologySuiteSetSearchParams({
          ...geologySuiteSearchParams,
          projectId: globalProjectId,
        });

        break;
      case ImportMappingType.Geophysics:
        geophysicsSuiteSetEnable(true);

        break;
      case ImportMappingType.Assay:
        assaySuiteSetEnable(true);
        assaySuiteSetSearchParams({
          ...assaySuiteSearchParams,
          projectId: globalProjectId,
        });
        break;
      case ImportMappingType.DownholeSurvey:
        downholeSurveyTypeSetEnable(true);
        break;
      default:
        geologySuiteSetEnable(false);
    }
  }, [importFileType, globalProjectId]);
  useEffect(() => {
    if (importFileType && suiteId) {
      requestPreviewMappingFields({
        importFileType: importFileType as any,
        suiteId: suiteId,
      });
    }
    if (
      importFileType === ImportMappingType.DownholeSurvey ||
      importFileType === ImportMappingType.DrillHole
    ) {
      requestPreviewMappingFields({
        importFileType: importFileType as any,
      });
    }
  }, [importFileType, suiteId]);
  const downholeSurveyTypeId = watch("downholeSurveyTypeId");
  useEffect(() => {
    if (importFileType === ImportMappingType.DownholeSurvey) {
      const defaultItem = downholeSurveyTypeData?.data?.items?.find(
        (item: any) => item.isDefault
      );
      if (defaultItem) {
        setValue("downholeSurveyTypeId", defaultItem.id);
      }
    }
  }, [importFileType, downholeSurveyTypeData]);

  const dispatch = useAppDispatch();
  useEffect(() => {
    if (suiteId) {
      geologySuiteRequest.getDetail(suiteId).then((res) => {
        dispatch(setGeologySuite(res?.data));
      });
    }
  }, [suiteId]);
  return (
    <ModalCommon
      open={openModalMapping}
      onCancel={() => setOpenModalMapping(false)}
      footer={null}
      closable={false}
    >
      <div className="flex flex-col gap-3 my-2">
        <div className="flex gap-2 flex-col ">
          <p className="text-14-14 font-semibold">Import file type</p>
          <Select
            options={ImportMappingTypeOptions}
            placeholder="Select a import file type"
            onChange={(value) => {
              setValue("ImportFileType", value);
              setValue("suiteId", undefined);
            }}
            value={importFileType}
            className="w-full"
          />
        </div>
        {importFileType === ImportMappingType.Geology && (
          <div className="flex gap-2 flex-col">
            <p className="text-14-14 font-semibold"> Geology Suite</p>
            <Select
              allowClear
              showSearch
              onSearch={(value) =>
                geologySuiteSetSearchParams({
                  ...geologySuiteSearchParams,
                  keyword: value,
                })
              }
              options={geologySuiteData?.data?.items?.map((item: any) => ({
                label: item.name,
                value: item.id,
              }))}
              placeholder="Select a geology suite"
              filterOption={false}
              onBlur={() =>
                geologySuiteSetSearchParams({
                  ...geologySuiteSearchParams,
                  keyword: "",
                })
              }
              onSelect={(value) => {
                setValue("suiteId", value);
                geologySuiteSetSearchParams({
                  ...geologySuiteSearchParams,
                  keyword: "",
                });
              }}
              value={suiteId}
            />
          </div>
        )}
        {importFileType === ImportMappingType.Geophysics && (
          <div className="flex gap-2 flex-col">
            <p className="text-14-14 font-semibold"> Geophysics Suite</p>
            <Select
              allowClear
              showSearch
              onSearch={(value) =>
                geophysicsSuiteSetSearchParams({
                  ...geophysicsSuiteSearchParams,
                  keyword: value,
                })
              }
              options={geophysicsSuiteData?.data?.result?.items?.map(
                (item: any) => ({
                  label: item.name,
                  value: item.id,
                })
              )}
              placeholder="Select a geophysics suite"
              filterOption={false}
              onBlur={() =>
                geophysicsSuiteSetSearchParams({
                  ...geophysicsSuiteSearchParams,
                  keyword: "",
                })
              }
              onSelect={(value) => {
                setValue("suiteId", value);
                geophysicsSuiteSetSearchParams({
                  ...geophysicsSuiteSearchParams,
                  keyword: "",
                });
              }}
              value={suiteId}
            />
          </div>
        )}
        {importFileType === ImportMappingType.Assay && (
          <div className="flex gap-2 flex-col">
            <p className="text-14-14 font-semibold"> Assay Suite</p>
            <Select
              allowClear
              showSearch
              onSearch={(value) =>
                assaySuiteSetSearchParams({
                  ...assaySuiteSearchParams,
                  keyword: value,
                })
              }
              options={assaySuiteData?.data?.items?.map((item: any) => ({
                label: item.name,
                value: item.id,
              }))}
              placeholder="Select a assay suite"
              filterOption={false}
              onBlur={() =>
                assaySuiteSetSearchParams({
                  ...assaySuiteSearchParams,
                  keyword: "",
                })
              }
              onSelect={(value) => {
                setValue("suiteId", value);
                assaySuiteSetSearchParams({
                  ...assaySuiteSearchParams,
                  keyword: "",
                });
              }}
              value={suiteId}
            />
          </div>
        )}
        <>
          {previewMappingFields && (
            <TemplateItem
              watch={watch}
              data={previewMappingFields}
              refetchTemplate={refetchTemplate}
              setValue={setValue}
              onSaveTemplate={() => {
                setOpenModalMapping(false);
              }}
              suiteId={suiteId}
              setOpenModalMapping={setOpenModalMapping}
            />
          )}
        </>
      </div>
    </ModalCommon>
  );
};
