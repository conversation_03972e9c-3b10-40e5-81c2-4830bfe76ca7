import { AppImages } from "@/common/configs";
import { IconZoomPan } from "@tabler/icons-react";
import { Button, Image, Tooltip } from "antd";
import { useState } from "react";
import { GrPowerReset } from "react-icons/gr";
import { WorkflowModal } from "./workflow-modal";

export interface IDrawImagePanelProps {
  img: any;
  imageConfig: any;
  setImageConfig: any;
  reset: any;
  refetch: any;
  queryFilter: any;
  setQueryFilter: any;
  isAdvance: boolean;
}

export function DrawImagePanel(props: IDrawImagePanelProps) {
  const {
    reset,
    imageConfig,
    setImageConfig,
    img,
    refetch,
    queryFilter,
    setQueryFilter,
    isAdvance,
  } = props;
  const [isOpenModal, setIsOpenModal] = useState(false);

  return (
    <div className="flex flex-col justify-start items-start gap-10 pt-10">
      {isOpenModal && (
        <WorkflowModal
          image={img}
          isOpenModal={isOpenModal}
          queryFilter={queryFilter}
          setQueryFilter={setQueryFilter}
          setIsOpenModal={setIsOpenModal}
          refetch={refetch}
        />
      )}

      <Tooltip title="Zoom and pan" placement="left">
        <Button
          type={imageConfig.isPanning ? "primary" : "text"}
          icon={<IconZoomPan style={{ width: 16, height: 16 }} />}
          onClick={() => {
            setImageConfig((prev: any) => {
              return {
                ...prev,
                isPanning: !prev.isPanning,
              };
            });
          }}
        ></Button>
      </Tooltip>
      <Tooltip title="Reset Zoom" placement="left">
        <Button
          type="text"
          icon={<GrPowerReset style={{ width: 16, height: 16 }} />}
          onClick={() => {
            reset();
          }}
        ></Button>
      </Tooltip>

      {isAdvance && (
        <Tooltip title="Workflow" placement="left">
          <Button
            icon={
              <Image
                preview={false}
                src={AppImages.icAuto}
                alt=""
                width={16}
                height={16}
              />
            }
            type="text"
            onClick={() => {
              setIsOpenModal(true);
            }}
            className="flex items-center"
          ></Button>
        </Tooltip>
      )}
    </div>
  );
}
