import React, { memo, useCallback, useState } from "react";
import { Input, But<PERSON>, Modal } from "antd";
import { Control, Controller } from "react-hook-form";
import { ErrorTooltip } from "./error-tooltip";
import { EditOutlined } from "@ant-design/icons";

interface FieldDescriptionProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldDescription = memo<FieldDescriptionProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [tempDescription, setTempDescription] = useState("");

    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation (but not within textarea for normal editing)
        if (event.key === "Tab" && !isModalVisible) {
          onKeyDown?.(event);
        }
      },
      [onKeyDown, isModalVisible]
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    const showModal = (currentValue: string) => {
      setTempDescription(currentValue || "");
      setIsModalVisible(true);
    };

    const handleOk = (
      onChange: (...event: any[]) => void,
      currentValue: string
    ) => {
      onChange(tempDescription);
      // Trigger row status update
      if (onFieldChange && typeof rowIndex === "number" && fieldPath) {
        onFieldChange(rowIndex, fieldPath, tempDescription);
      }
      setIsModalVisible(false);
    };

    const handleCancel = () => {
      setIsModalVisible(false);
    };

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full flex items-center">
            <Input
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Description"
              className={`w-full ${className || ""}`}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              title={field.value || ""} // Show full text on hover
              style={{
                height: "36px",
                overflow: "hidden",
                textOverflow: "ellipsis",
                whiteSpace: "nowrap",
                marginRight: "4px", // Add some space before the button
              }}
              onChange={(e) => {
                field.onChange(e);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, e.target.value);
                }
              }}
            />
            <Button
              icon={<EditOutlined />}
              onClick={() => showModal(field.value)}
              disabled={disabled}
              aria-label="Edit Description"
            />
            <ErrorTooltip error={error} />
            <Modal
              title="Edit Description"
              visible={isModalVisible}
              onOk={() => handleOk(field.onChange, field.value)}
              onCancel={handleCancel}
              okText="OK"
              cancelText="Cancel"
              // style={{ top: 20, right: 20, position: 'fixed', margin: 0 }} // Right-side anchoring if needed
              width={600} // Adjust width as needed
            >
              <Input.TextArea
                rows={10} // Adjust rows as needed for a large text area
                value={tempDescription}
                onChange={(e) => setTempDescription(e.target.value)}
                placeholder="Enter full description"
              />
            </Modal>
          </div>
        )}
      />
    );
  }
);

FieldDescription.displayName = "FieldDescription";
