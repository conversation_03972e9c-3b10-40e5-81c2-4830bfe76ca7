#!/usr/bin/env node

/**
 * Build Validation Script
 * Validates that the optimized build maintains functionality
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Validating optimized build...\n');

// Check if build completed successfully
const buildDir = path.join(process.cwd(), '.next');
if (!fs.existsSync(buildDir)) {
  console.error('❌ Build directory not found');
  process.exit(1);
}

// Check for critical build artifacts
const criticalFiles = [
  '.next/static',
  '.next/server',
  '.next/BUILD_ID',
];

let allFilesExist = true;
criticalFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file} exists`);
  } else {
    console.log(`❌ ${file} missing`);
    allFilesExist = false;
  }
});

// Check bundle analyzer reports
const analyzeDir = path.join(process.cwd(), '.next/analyze');
if (fs.existsSync(analyzeDir)) {
  console.log('✅ Bundle analyzer reports generated');
  const reports = fs.readdirSync(analyzeDir);
  reports.forEach(report => {
    console.log(`   📊 ${report}`);
  });
} else {
  console.log('ℹ️  Bundle analyzer reports not found (run yarn build:analyze)');
}

// Check for optimized chunks
const staticDir = path.join(process.cwd(), '.next/static/chunks');
if (fs.existsSync(staticDir)) {
  const chunks = fs.readdirSync(staticDir).filter(f => f.endsWith('.js'));
  console.log(`✅ Generated ${chunks.length} JavaScript chunks`);
  
  // Look for our optimized chunks
  const vendorChunk = chunks.find(c => c.includes('vendor'));
  const uiChunk = chunks.find(c => c.includes('5516')); // Ant Design chunk
  
  if (vendorChunk) console.log(`   📦 Vendor chunk: ${vendorChunk}`);
  if (uiChunk) console.log(`   🎨 UI chunk: ${uiChunk}`);
}

// Validate package.json scripts
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const requiredScripts = ['build', 'build:analyze'];
requiredScripts.forEach(script => {
  if (packageJson.scripts[script]) {
    console.log(`✅ Script "${script}" configured`);
  } else {
    console.log(`❌ Script "${script}" missing`);
    allFilesExist = false;
  }
});

// Check environment variables
const envLocal = path.join(process.cwd(), '.env.local');
if (fs.existsSync(envLocal)) {
  const envContent = fs.readFileSync(envLocal, 'utf8');
  const optimizations = [
    'NEXT_TELEMETRY_DISABLED',
    'GENERATE_SOURCEMAP',
    'NEXT_PRIVATE_BUILD_CACHE'
  ];
  
  optimizations.forEach(opt => {
    if (envContent.includes(opt)) {
      console.log(`✅ Environment optimization: ${opt}`);
    } else {
      console.log(`⚠️  Missing environment optimization: ${opt}`);
    }
  });
}

console.log('\n📋 Validation Summary:');
if (allFilesExist) {
  console.log('✅ All critical build artifacts present');
  console.log('✅ Build optimization successful');
  console.log('\n🚀 Ready for deployment!');
  process.exit(0);
} else {
  console.log('❌ Some critical files missing');
  console.log('❌ Build validation failed');
  process.exit(1);
}
