import React, { useState, memo, useCallback } from "react";
import { Button } from "antd";

// Simple performance monitoring component (safe, no auto-tracking)
export const PerformanceMonitor = memo(() => {
  const [stats, setStats] = useState({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
  });

  // Safe reset function that doesn't cause re-render loops
  const resetStats = useCallback(() => {
    setStats({
      renderCount: 0,
      lastRenderTime: 0,
      averageRenderTime: 0,
    });
  }, []);

  // Manual performance tracking button for testing
  const simulateRender = useCallback(() => {
    const renderTime = 1 + Math.random() * 15; // Simulate 1-16ms render
    setStats((prev) => ({
      renderCount: prev.renderCount + 1,
      lastRenderTime: renderTime,
      averageRenderTime:
        prev.renderCount === 0
          ? renderTime
          : (prev.averageRenderTime * prev.renderCount + renderTime) /
            (prev.renderCount + 1),
    }));
  }, []);

  return (
    <div className="fixed top-4 right-4 bg-white border border-gray-300 rounded-lg p-4 shadow-lg z-50 text-sm">
      <h3 className="font-bold mb-2">Performance Monitor</h3>
      <div className="space-y-1">
        <div>Renders: {stats.renderCount}</div>
        <div>Last: {stats.lastRenderTime.toFixed(2)}ms</div>
        <div>Avg: {stats.averageRenderTime.toFixed(2)}ms</div>
        <div
          className={`font-semibold ${
            stats.averageRenderTime > 16 ? "text-red-600" : "text-green-600"
          }`}
        >
          {stats.averageRenderTime > 16 ? "⚠️ Slow" : "✅ Fast"}
        </div>
      </div>
      <div className="flex gap-2 mt-2">
        <Button size="small" onClick={simulateRender} type="primary">
          Simulate
        </Button>
        <Button size="small" onClick={resetStats}>
          Reset
        </Button>
      </div>
    </div>
  );
});

PerformanceMonitor.displayName = "PerformanceMonitor";

// Scroll performance tester
export const ScrollPerformanceTester = memo(
  ({ onTest }: { onTest: (result: any) => void }) => {
    const [testing, setTesting] = useState(false);

    const runScrollTest = async () => {
      setTesting(true);

      const container = document.querySelector(".high-performance-table-body");
      if (!container) {
        onTest({ error: "Table container not found" });
        setTesting(false);
        return;
      }

      const startTime = performance.now();
      const frameRates: number[] = [];
      let lastFrameTime = startTime;
      let frameCount = 0;

      const measureFrame = () => {
        const currentTime = performance.now();
        const frameDuration = currentTime - lastFrameTime;
        frameRates.push(1000 / frameDuration);
        lastFrameTime = currentTime;
        frameCount++;
      };

      // Simulate scrolling
      const scrollHeight = container.scrollHeight;
      const scrollStep = scrollHeight / 100;

      for (let i = 0; i < 100; i++) {
        container.scrollTop = i * scrollStep;
        measureFrame();
        await new Promise((resolve) => requestAnimationFrame(resolve));
      }

      const endTime = performance.now();
      const totalTime = endTime - startTime;
      const averageFPS =
        frameRates.reduce((sum, fps) => sum + fps, 0) / frameRates.length;
      const minFPS = Math.min(...frameRates);

      onTest({
        totalTime,
        averageFPS,
        minFPS,
        frameCount,
        smooth: minFPS > 30 && averageFPS > 45,
      });

      setTesting(false);
    };

    return (
      <Button
        onClick={runScrollTest}
        loading={testing}
        type="primary"
        className="mb-4"
      >
        Test Scroll Performance
      </Button>
    );
  }
);

ScrollPerformanceTester.displayName = "ScrollPerformanceTester";
