import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/common/modal-common";
import { <PERSON>ton, InputNumber } from "antd";
import { useEffect, useRef, useState } from "react";
import {
  RiCloseCircleLine,
  RiDeleteBinLine,
  RiSave3Line,
} from "react-icons/ri";

interface IModalEditDepthProps {
  modalState: any;
  setModalState: (state: any) => void;
  onEnterChangeText: (data: any, value: any) => void;
  onDeleteOcr: (id: any) => void;
  setDirectOCRdata: (data: any) => void;
}

export function ModalEditDepth({
  modalState,
  onEnterChangeText,
  onDeleteOcr,
  setModalState,
  setDirectOCRdata,
}: IModalEditDepthProps) {
  const inputRef = useRef<any>(null);

  const handleCancel = () => {
    setModalState({ isOpen: false, data: null });
  };
  const [inputValue, setInputValue] = useState<string>(
    modalState.data?.text ?? ""
  );

  // Auto focus input when modal opens
  useEffect(() => {
    if (modalState.isOpen && inputRef.current) {
      // Use setTimeout to ensure the modal is fully rendered
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [modalState.isOpen]);

  // Update input value when modal data changes
  useEffect(() => {
    setInputValue(modalState.data?.text ?? "");
  }, [modalState.data]);

  const handleDelete = () => {
    setDirectOCRdata((prev) =>
      prev.filter((item: any) => item.id !== modalState.data.id)
    );

    onDeleteOcr(modalState.data.id);
    handleCancel();
  };
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      <div className="flex flex-col gap-2">
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18">Depth</p>
          <InputNumber
            ref={inputRef}
            className="w-full"
            value={inputValue}
            onChange={(e) => {
              setInputValue(e ?? "");
            }}
            onPressEnter={() => {
              onEnterChangeText(modalState.data, inputValue);
              handleCancel();
            }}
          />
        </div>
        <div className="flex justify-end gap-2">
          <Button icon={<RiCloseCircleLine />} onClick={handleCancel}>
            Cancel
          </Button>
          <Button
            type="dashed"
            icon={<RiDeleteBinLine />}
            onClick={handleDelete}
          >
            Delete
          </Button>
          <Button
            onClick={() => {
              onEnterChangeText(modalState.data, inputValue);
              handleCancel();
            }}
            type="primary"
            icon={<RiSave3Line />}
          >
            Save
          </Button>
        </div>
      </div>
    </ModalCommon>
  );
}
