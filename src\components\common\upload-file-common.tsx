"use client";
import { UploadOutlined } from "@ant-design/icons";
import { Button, Select, Upload } from "antd";
import { FormItem } from "react-hook-form-antd";

export interface IInputTextCommonProps {
  label?: string;
  name: string;
  placeholder?: string;
  control?: any;
  disabled?: boolean;
  onChange?: (value: any) => void;
  onUploadAsync?: (file: any) => Promise<void>;
  file?: any;
  horizontal?: boolean;
  isRequired?: boolean;
}

export function UploadCommon(props: IInputTextCommonProps) {
  const {
    label,
    placeholder,
    disabled,
    onChange,
    onUploadAsync,
    file,
    horizontal,
    isRequired,
  } = props;
  return (
    <div
      className={`flex ${
        horizontal ? "flex-row items-center w-full" : "flex-col"
      } gap-2`}
    >
      <div className="flex">
        {label && <p className="font-medium">{label}</p>}
        {isRequired && (
          <span className="text-14-16 md:text-16-20 text-error">*</span>
        )}
      </div>
      <Upload
        action={(file) => {
          return new Promise(() => {
            onUploadAsync && onUploadAsync(file);
          });
        }}
        name="upload"
        className="upload-file w-full"
        showUploadList={false}
        onChange={onChange}
        disabled={disabled}
        accept=".xlsx"
      >
        {file ? (
          <Button className=" w-full text-start">
            <p className="truncate">{file.name}</p>
          </Button>
        ) : (
          <Button className=" w-full text-start" icon={<UploadOutlined />}>
            {placeholder}
          </Button>
        )}
      </Upload>
    </div>
  );
}
