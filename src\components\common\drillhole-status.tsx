import { DrillholeEnum } from "@/modules/drillhole/model/enum/drillhole.enum";
import { Tag } from "antd";
import { ReactNode } from "react";
interface DrillHoleStatusProps {
  status: DrillholeEnum;
  prefix?: ReactNode;
  style?: React.CSSProperties;
}
function DrillHoleStatus({ status, prefix, style }: DrillHoleStatusProps) {
  switch (status) {
    case DrillholeEnum.NotStarted:
      return (
        <Tag color="orange" style={style}>
          {prefix} Not Started
        </Tag>
      );
    case DrillholeEnum.InProgress:
      return (
        <Tag color="blue" style={style}>
          {prefix} In Progress
        </Tag>
      );
    case DrillholeEnum.ReProcess:
      return (
        <Tag color="gold" style={style}>
          {prefix} Reprocess
        </Tag>
      );
    case DrillholeEnum.Review:
      return (
        <Tag color="purple" style={style}>
          {prefix} Review
        </Tag>
      );
    case DrillholeEnum.Complete:
      return (
        <Tag color="green" style={style}>
          {prefix} Complete
        </Tag>
      );
    case DrillholeEnum.Exported:
      return (
        <Tag color="success" style={style}>
          {prefix} Exported
        </Tag>
      );
    case DrillholeEnum.Unnamed:
      return (
        <Tag color="volcano-inverse" style={style}>
          {prefix} Unnamed
        </Tag>
      );
    default:
      return "";
  }
}

export default DrillHoleStatus;
