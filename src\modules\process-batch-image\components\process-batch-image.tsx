"use client";
import useEffectAfterMounted from "@/common/hooks/useSkipFirstRender";
import { SortOrder } from "@/common/interfaces/general/general.types";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { getSignalRConnection } from "@/lib/signalr.helper";
import { useGetListDrillhole } from "@/modules/drillhole/hooks/useGetListDrillHole.hook";
import { drillHoleStatusOptions } from "@/modules/drillhole/model/enum/drillhole.enum";
import { useQueryImageSubType } from "@/modules/image-type/hooks/useGetQueryImageSubType";
import { useQueryImageType } from "@/modules/image-type/hooks/useQueryImageType";
import ProgressBar from "@/modules/image/components/progress-bar";
import { imageCategoryOptions } from "@/modules/image/constants/image.constant";
import { useGetListProject } from "@/modules/projects/hooks";
import { useGetListProspect } from "@/modules/prospect/hooks/useGetListProspect";
import { useCancelJob } from "@/modules/workflow-job/hooks/useCancelJob";
import { useExecuteBatch } from "@/modules/workflow-job/hooks/useExecuteBatch";
import { useGetDetailWorkflowJob } from "@/modules/workflow-job/hooks/useGetDetailWorkflowJob";
import { useRerunJob } from "@/modules/workflow-job/hooks/useRerunJob";
import { useUpdateJob } from "@/modules/workflow-job/hooks/useUpdateJob";
import { useGetListWorkflows } from "@/modules/workflows/hooks/useGetListWorkflow.hook";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import { zodResolver } from "@hookform/resolvers/zod";
import { HubConnectionState } from "@microsoft/signalr";
import { Button, Form, Spin, Tag } from "antd";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { ProcessBatchStatus } from "../const/process-batch.const";
import {
  ProcessBatchBody,
  ProcessBatchBodyType,
} from "../model/process-batch.schema";

export interface IProcessBatchImageProps {
  id?: string;
}

export function ProcessBatchImage({ id }: IProcessBatchImageProps) {
  const router = useRouter();

  const { data, request, setData } = useGetDetailWorkflowJob();
  const { request: requestGetListProject, data: projects } =
    useGetListProject();

  const { request: requestGetListProspect, data: prospects } =
    useGetListProspect();
  const {
    request: requestGetListDrillHole,
    loading: loadingListDrillhole,
    data: drillholes,
  } = useGetListDrillhole();

  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const [maxResultCountProspect, setMaxResultCountProspect] = useState(10);
  const [maxResultCountDrillhole, setMaxResultCountDrillhole] = useState(10);
  const [maxResultCountWorkflow, setMaxResultCountWorkflow] = useState(10);
  const [keywordProject, setKeywordProject] = useState("");
  const [keywordProspect, setKeywordProspect] = useState("");
  const [keywordDrillhole, setKeywordDrillhole] = useState("");
  const [keywordWorkflow, setKeywordWorkflow] = useState("");

  const { control, handleSubmit, setValue, watch, reset } =
    useForm<ProcessBatchBodyType>({
      resolver: zodResolver(ProcessBatchBody),
    });
  const projectId = watch("projectId");
  const prospectId = watch("prospectId");

  const { request: getListWorkflow, data: workflows } = useGetListWorkflows();

  useEffect(() => {
    getListWorkflow({
      maxResultCount: maxResultCountWorkflow,
      skipCount: 0,
      isActive: true,
      keyword: keywordWorkflow,
      sortField: "name",
      sortOrder: "asc",
    });
  }, [maxResultCountWorkflow, keywordWorkflow]);

  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };

  const handleScrollProspect = (e: any) => {
    const ele = e.target;
    if (ele.scrollTop + ele.clientHeight === ele.scrollHeight) {
      setMaxResultCountProspect((prev) => prev + 10);
    }
  };

  const handleScrollDrillhole = (e: any) => {
    const ele = e.target;
    if (ele.scrollTop + ele.clientHeight === ele.scrollHeight) {
      setMaxResultCountDrillhole((prev) => prev + 10);
    }
  };
  const handleScrollWorkflows = (e: any) => {
    const ele = e.target;
    if (ele.scrollTop + ele.clientHeight === ele.scrollHeight) {
      setMaxResultCountWorkflow((prev) => prev + 10);
    }
  };

  const { request: requestExecuteBatch, loading: loadingExecuteBatch } =
    useExecuteBatch();
  const { request: requestRerunJob, loading: loadingRerunJob } = useRerunJob();

  useEffect(() => {
    if (id) {
      request({
        Id: id,
      });
    }
  }, [id]);

  useEffect(() => {
    if (data) {
      reset({
        ...data,
        imageTypeId: data.imageType?.id,
        imageSubTypeId: data.imageSubtype?.id,
        statusDone: data.statusDone !== 0 ? data.statusDone : undefined,
        statusFilter: data.statusFilter !== 0 ? data.statusFilter : undefined,
      });
    }
  }, [data]);

  const onSubmit = (data: ProcessBatchBodyType) => {
    if (id) {
      requestRerunJob(
        {
          id: Number(id),
        },
        () => {
          toast.success("Rerun started", { autoClose: 200 });
          request({
            Id: id,
          });
        },
        () => {
          toast.error("Rerun failed");
        }
      );
    } else {
      requestExecuteBatch(data, () => {
        toast.success("Batch processing started", { autoClose: 200 });
        router.push("/process-batch-images");
      });
    }
  };
  const renderStats = (status: number) => {
    switch (status) {
      case 1:
        return (
          <Tag
            icon={<CloseCircleOutlined />}
            color="blue"
            style={{
              fontFamily: "Visby",
              fontWeight: 800,
              borderRadius: 20,
              fontSize: 20,
            }}
            className="flex items-center"
          >
            Not Start
          </Tag>
        );
      case 2:
        return (
          <Tag
            style={{
              fontFamily: "Visby",
              fontWeight: 800,
              borderRadius: 20,
              fontSize: 20,
            }}
            icon={<LoadingOutlined />}
            color="processing"
            className="flex items-center"
          >
            Running
          </Tag>
        );

      case 3:
        return (
          <Tag
            style={{
              fontFamily: "Visby",
              borderRadius: 20,
              fontSize: 20,
              fontWeight: 800,
            }}
            icon={<CheckCircleOutlined />}
            color="success"
            className="flex items-center"
          >
            Completed
          </Tag>
        );

      case 4:
        return (
          <Tag
            style={{
              fontFamily: "Visby",
              borderRadius: 20,
              fontSize: 20,
              fontWeight: 800,
            }}
            icon={<CloseCircleOutlined />}
            color="error"
            className="flex items-center"
          >
            Failed
          </Tag>
        );
      case 5:
        return (
          <Tag
            style={{
              fontFamily: "Visby",
              borderRadius: 20,
              fontSize: 20,
              fontWeight: 800,
            }}
            icon={<CloseCircleOutlined />}
            color="volcano"
            className="flex items-center"
          >
            Canceled
          </Tag>
        );

      default:
        break;
    }
  };

  const { loading: loadingCancelJob, request: requestCancelJob } =
    useCancelJob();

  const oncancel = () => {
    if (!id) {
    } else {
      requestCancelJob(
        { id: id },
        (res: any) => {
          toast.success("Batch processing canceled", { autoClose: 200 });
          request({
            Id: id,
          });
        },
        () => {
          toast.error("Batch processing cancel failed", { autoClose: 200 });
        }
      );
    }
  };

  // connect to signalr
  const signalRConnection = useMemo(() => getSignalRConnection(), [id]);
  useEffect(() => {
    if (!id) {
      return;
    }
    (async () => {
      try {
        await signalRConnection?.start();
      } catch (error) {}
      if (signalRConnection.state !== HubConnectionState.Connected) return;
      signalRConnection?.invoke("JoinGroups", [id]);
      signalRConnection?.on("WorkflowJobSocket", (data) => {
        const res = JSON.parse(data);
        setData((prev) => {
          return {
            ...prev,
            status: res.status,
            totalCompleted: res.totalCompleted,
            totalErrors: res.totalErrors,
            totalImage: res.totalImage,
          };
        });
      });
    })();
    return () => {
      signalRConnection?.stop();
    };
  }, [id]);
  const [isEdit, setIsEdit] = useState(!Boolean(id));
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { request: requestUpdateJob, loading: loadingUpdateJob } =
    useUpdateJob();
  const onClickUpdateBatch = () => {
    handleSubmit(async (data: ProcessBatchBodyType) => {
      requestUpdateJob(
        {
          ...data,
          id: id,
        },
        () => {
          setIsModalVisible(false);
          toast.success("Update batch processing successfully", {
            autoClose: 200,
          });
          setIsEdit(false);
          request(
            {
              Id: id,
            },
            (res: any) => {
              setData(res);
            }
          );
        }
      );
    })();
  };

  const isShowSaveButton = id ? data?.status !== 2 : false;

  useEffect(() => {
    requestGetListProject({
      maxResultCount: maxResultCountProject,
      skipCount: 0,
      sortField: "name",
      sortOrder: SortOrder.ASC,
      keyword: keywordProject,
    });
  }, [maxResultCountProject, keywordProject]);
  useEffect(() => {
    if (projectId) {
      requestGetListProspect({
        maxResultCount: maxResultCountProspect,
        skipCount: 0,
        keyword: keywordProspect,
        projectIds: projectId ? [projectId] : undefined,
      });
    }
  }, [projectId, keywordProspect, maxResultCountProspect]);

  useEffectAfterMounted(() => {
    requestGetListDrillHole({
      skipCount: 0,
      maxResultCount: maxResultCountDrillhole,
      projectIds: projectId ? [projectId] : undefined,
      prospectIds: prospectId ? [prospectId] : undefined,
      keyword: keywordDrillhole,
    });
  }, [maxResultCountDrillhole, keywordDrillhole, projectId, prospectId]);
  const globalProjectId = useAppSelector(
    (state) => state.user.userInfo.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo.prospectId
  );
  useEffect(() => {
    setValue("projectId", globalProjectId || 0);
    setValue("prospectId", globalProspectId || 0);
  }, [globalProjectId, globalProspectId]);
  const { data: imageTypes } = useQueryImageType();
  const imageTypeOptions = useMemo(
    () =>
      imageTypes?.data?.items.map((item) => ({
        label: item.name,
        value: item.id,
      })) || [],
    [imageTypes?.data?.items]
  );
  const imageTypeId = watch("imageTypeId");

  const { data: imageSubTypes, setSearchParams: setSearchParamsImageSubType } =
    useQueryImageSubType();
  const imageSubTypeOptions = useMemo(() => {
    if (imageTypeId) {
      return (
        imageSubTypes?.data?.items.map((item) => ({
          label: item.name,
          value: item.id,
        })) || []
      );
    }
    return [];
  }, [imageSubTypes?.data?.items, imageTypeId]);
  useEffect(() => {
    if (imageTypeId) {
      setSearchParamsImageSubType({
        imageTypeId: imageTypeId,
      });
    }
  }, [imageTypeId]);
  return (
    <Form
      onFinish={handleSubmit(onSubmit, (err) => {
        console.log(err);
      })}
    >
      <ModalCommon
        open={isModalVisible}
        centered
        padding={0}
        footer={null}
        onCancel={() => {
          setIsModalVisible(false);
        }}
        style={{ borderRadius: 8 }}
        width={450}
        closable={false}
      >
        <p>
          Do you want to save this batch processing? You can't undo this action
        </p>
        <div className="flex justify-end gap-2">
          <Button
            onClick={() => {
              setIsModalVisible(false);
            }}
            className="btn btn-sm"
          >
            No
          </Button>
          <Button
            loading={loadingUpdateJob}
            // htmlType="submit"
            className="btn btn-sm bg-primary text-white hover:bg-primary"
            onClick={onClickUpdateBatch}
          >
            Yes
          </Button>
        </div>
      </ModalCommon>

      <div className="flex flex-col gap-3">
        <div className="flex flex-col sm:flex-row justify-between gap-4 sm:items-center">
          <p className="text-xl md:text-2xl font-extrabold uppercase">
            {id ? "Detail batch processing" : "Create Batch processing"}
          </p>
          {data && renderStats(data?.status)}
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          <div className="xl:col-span-2">
            <div className="grid md:grid-cols-2 gap-4">
              <InputTextCommon
                control={control}
                name="name"
                label="Name"
                size="large"
                placeholder="Name"
                isRequired
              />
              <InputTextCommon
                control={control}
                name="description"
                label="Description"
                size="large"
                placeholder="Description"
                isRequired
              />
              <SelectCommon
                name="projectId"
                label="Project"
                control={control}
                isRequired
                onSearch={(value) => {
                  setKeywordProject(value);
                }}
                onBlur={() => {
                  setKeywordProject("");
                }}
                options={projects.map((project) => {
                  return {
                    label: project.name,
                    value: project.id,
                  };
                })}
                onPopupScroll={handleScrollProject}
                filterOption={false}
                showSearch
                placeholder="Select project"
                size="large"
                allowClear
              />
              <SelectCommon
                label="Prospect"
                isRequired
                control={control}
                name="prospectId"
                onSearch={(value) => {
                  setKeywordProspect(value);
                }}
                onBlur={() => {
                  setKeywordProspect("");
                }}
                options={prospects?.map((prospect) => {
                  return {
                    label: prospect.name,
                    value: prospect.id,
                  };
                })}
                filterOption={false}
                showSearch
                placeholder="Select prospect"
                onPopupScroll={handleScrollProspect}
                size="large"
                allowClear
              />
              <SelectCommon
                isRequired
                control={control}
                name="drillholeId"
                label="Drillhole"
                onSearch={(value) => {
                  setKeywordDrillhole(value);
                }}
                onBlur={() => {
                  setKeywordDrillhole("");
                }}
                searchValue={keywordDrillhole}
                options={drillholes?.map((drillhole) => {
                  return {
                    label: drillhole.name,
                    value: drillhole.id,
                  };
                })}
                filterOption={false}
                showSearch
                placeholder="Select drillhole"
                onPopupScroll={handleScrollDrillhole}
                size="large"
                allowClear
                notFoundContent={
                  loadingListDrillhole ? <Spin size="small" /> : <>Not found</>
                }
              />

              <SelectCommon
                control={control}
                name="statusFilter"
                label="Image Status"
                options={drillHoleStatusOptions}
                placeholder="Image Status"
                size="large"
                allowClear
              />
              <SelectCommon
                control={control}
                options={drillHoleStatusOptions}
                placeholder="Image Status when complete"
                size="large"
                allowClear
                name="statusDone"
                label="Image Status when complete"
              />

              <SelectCommon
                control={control}
                options={imageCategoryOptions}
                placeholder="Image Category"
                size="large"
                allowClear
                name="imageCategory"
                label="Image Category"
              />
              <SelectCommon
                control={control}
                options={imageTypeOptions}
                placeholder="Image Type"
                size="large"
                allowClear
                name="imageTypeId"
                label="Image Type"
              />
              <SelectCommon
                control={control}
                options={imageSubTypeOptions}
                placeholder="Image Subtype"
                size="large"
                allowClear
                name="imageSubTypeId"
                label="Image Subtype"
              />
            </div>
            <div className="grid md:grid-cols-2 gap-4 col-span-3 mt-4">
              <SelectCommon
                label="Workflow"
                control={control}
                isRequired
                name="workflowId"
                options={workflows?.map((workflow) => {
                  return {
                    label: workflow.name,
                    value: workflow.id,
                  };
                })}
                placeholder="Select workflow"
                size="large"
                allowClear
                showSearch
                onPopupScroll={handleScrollWorkflows}
                onSearch={(value) => {
                  setKeywordWorkflow(value);
                }}
                filterOption={false}
                searchValue={keywordWorkflow}
                onBlur={() => {
                  setKeywordWorkflow("");
                }}
              />
            </div>
            {isShowSaveButton && (
              <Button
                onClick={() => {
                  setIsModalVisible(true);
                }}
                className="w-20 mt-4"
                type="primary"
                icon={<SaveOutlined />}
              >
                Save
              </Button>
            )}
          </div>
          <div className="xl:col-span-1 w-full flex justify-center items-center flex-col gap-6 py-6">
            {([ProcessBatchStatus.NotStart].includes(data?.status) || !data) &&
              !id && (
                <button
                  type="submit"
                  className="btn bg-primary hover:bg-primary-hover rounded-full h-24 w-24 md:h-32 md:w-32 flex justify-center items-center text-white uppercase font-bold text-sm md:text-base p-2 md:p-4"
                >
                  Process
                </button>
              )}

            {[
              ProcessBatchStatus.Canceled,
              ProcessBatchStatus.Failed,
              ProcessBatchStatus.Completed,
            ].includes(data?.status) && (
              <button
                type="submit"
                className="btn bg-primary hover:bg-primary-hover rounded-full h-24 w-24 md:h-32 md:w-32 flex justify-center items-center text-white uppercase font-bold text-sm md:text-base p-2 md:p-4"
              >
                Reprocess
              </button>
            )}

            {[ProcessBatchStatus.Running].includes(data?.status) && (
              <div
                onClick={oncancel}
                className="btn cursor-pointer bg-red-400 hover:bg-red-500 rounded-full h-24 w-24 md:h-32 md:w-32 flex justify-center items-center text-white uppercase font-bold text-sm md:text-base p-2 md:p-4"
              >
                Cancel
              </div>
            )}

            {data && (
              <ProgressBar
                completed={data?.totalCompleted}
                errors={data?.totalErrors}
                total={data?.totalImage}
              />
            )}
          </div>
        </div>
      </div>
    </Form>
  );
}
