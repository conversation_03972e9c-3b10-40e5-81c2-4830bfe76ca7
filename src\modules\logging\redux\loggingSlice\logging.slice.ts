import { RequestState } from "@/common/configs/app.contants";
import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { CONFIG } from "../../constants/logging.constants";
import {
  LoggingContextMenu,
  LoggingInfoInterface,
  LoggingLine,
  PreviewImage,
} from "../../interface/logging.interface";
import { SegmentationResult } from "../../model/dtos/logging.config";
import {
  EnumCalculateClickedDepth,
  EnumLoggingExtraViews,
  EnumLoggingViews,
} from "../../model/enum/logging.enum";
import {
  getGeologyData,
  getExtraViewModeImages,
  getLoggingInfo,
  getLoggingInfoInfinite,
} from "./thunks";
import { Recovery } from "../../types/logging.types";
import { RockLineType } from "../../types/logging.types";
import { isNumber } from "lodash";

const initialState: AccountSettingsSliceState = {
  allLoggings: [],
  allLoggingsTable: [],
  reviewDepthFrom: 0,
  lines: [],

  depthFrom: 0,
  depthTo: 0,
  skipCount: 0,
  infiniteScrollState: {
    currentPage: 0,
    totalItems: 0,
    hasMore: true,
    loading: false,
  },
  contextMenu: {
    visible: false,
    isRightClick: false,
  },
  hasShownMeasureInstructions: false,
  previewImage: {
    visible: false,
    url: "",
  },
  imageRows: [],
  imageExtraRows: [],
  extraViews: [],
  loggingViewMode: EnumLoggingViews.Image,
  segmentations: [],
  isShowSegmentation: false,
  isShowPercentageRecovery: false,
  calculateClickedDepth: EnumCalculateClickedDepth.ApiCalculation,

  imageGap: 200,
  refetchLoggingView: false,
  displayColumn: CONFIG.DEFAULT_DISPLAY_COLUMN,
  offsetY: undefined,
  targetDepth: undefined,
  loggingSuiteMode: "Geology",
  openModalGeotechData: false,
  geotechData: [],
  geotechDataToEdit: null,
  refetchGeotechData: false,
  startPointLineInterval: null,
  measurePointsInterval: {},
  measureXPointsInterval: {},
  loadingGeotechData: false,
  recoveries: [],
  trayDepths: [],
  toggleUpdateOCR: false,
  isSplitLine: false,
  isMergeLine: false,
  isDeleteLine: false,
  selectedLine: null,
  selectedRockLineType: RockLineType.Recovery,
  isAddLine: false,
  loggingSuiteSelected: null,
  geotechSelected: null,
  segmentationsDetail: [],
};

export const loggingSlice = createSlice({
  name: "logging",
  initialState,
  reducers: {
    initLogging: (state, action: PayloadAction<number>) => {
      state.reviewDepthTo = undefined;
      state.lines = [];
    },

    updateReviewDepthFrom: (state, action: PayloadAction<number | null>) => {
      state.reviewDepthFrom = action.payload;
    },
    updateImageGap: (state, action: PayloadAction<number>) => {
      state.imageGap = action.payload;
    },
    updateCalculateClickedDepth: (
      state,
      action: PayloadAction<EnumCalculateClickedDepth>
    ) => {
      state.calculateClickedDepth = action.payload;
    },
    updateIsShowSegmentation: (state, action: PayloadAction<boolean>) => {
      state.isShowSegmentation = action.payload;
    },
    updateIsShowPercentageRecovery: (state, action: PayloadAction<boolean>) => {
      state.isShowPercentageRecovery = action.payload;
    },
    updateSelectedDrilhole: (state, action: PayloadAction<any>) => {
      state.selectedDrillHole = action.payload;
    },

    updatePreviousLoggingDepth: (state, action: PayloadAction<number>) => {
      state.previousLoggingDepth = action.payload;
    },

    updateReviewDepthTo: (state, action: PayloadAction<number>) => {
      state.reviewDepthTo = action.payload;
    },
    updateLoggingViewMode: (state, action: PayloadAction<EnumLoggingViews>) => {
      state.loggingViewMode = action.payload;
    },

    updateLoggingLine: (state, action: PayloadAction<LoggingLine>) => {
      const index = state.lines.findIndex(
        (line) => line.id === action.payload.id
      );
      if (index !== -1) {
        state.lines[index] = action.payload;
      }
    },
    addLoggingLine: (state, action: PayloadAction<LoggingLine>) => {
      state.lines = [...state.lines, action.payload];
    },

    updateSegmentsResult: (
      state,
      action: PayloadAction<SegmentationResult[]>
    ) => {
      state.segmentations = action.payload;
    },
    updateSegmentsDetailResult: (
      state,
      action: PayloadAction<SegmentationResult[]>
    ) => {
      state.segmentationsDetail = action.payload;
    },

    saveLoggingLine: (state) => {
      state.lines = state.lines.map((line) => ({
        ...line,
        isSave: true,
      }));
    },
    updateCurrentDepth: (
      state,
      action: PayloadAction<{ depthFrom?: number; depthTo?: number }>
    ) => {
      isNumber(action.payload.depthFrom) &&
        (state.depthFrom = action.payload.depthFrom);
      isNumber(action.payload.depthTo) &&
        (state.depthTo = action.payload.depthTo);
    },

    saveLoggingData: (state, action: PayloadAction<LoggingInfoInterface>) => {
      state.reviewDepthFrom = action.payload.depthTo ?? 0;
      state.reviewDepthTo = undefined;
    },

    updateLoggingContextMenu: (
      state,
      action: PayloadAction<Partial<LoggingContextMenu>>
    ) => {
      state.contextMenu = {
        ...state.contextMenu,
        ...action.payload,
      };
    },

    updatePreviewImageUrl: (
      state,
      action: PayloadAction<Partial<PreviewImage>>
    ) => {
      state.previewImage = {
        ...state.previewImage,
        ...action.payload,
      };
    },

    setImageRows: (state, action: PayloadAction<any[]>) => {
      state.imageRows = action.payload;
    },

    setImageHyperRows: (state, action: PayloadAction<any[]>) => {
      state.imageExtraRows = action.payload;
    },

    updateExtraViews: (
      state,
      action: PayloadAction<EnumLoggingExtraViews[]>
    ) => {
      state.extraViews = action.payload;
    },
    updateSelectedSuite: (state, action: PayloadAction<string>) => {
      state.selectedSuite = action.payload;
    },
    updateRefetchLoggingView: (state) => {
      if (state.refetchLoggingView) {
        state.refetchLoggingView = false;
      } else {
        state.refetchLoggingView = true;
      }
    },
    updateLoggingSuiteMode: (
      state,
      action: PayloadAction<"Geology" | "Geotech" | null>
    ) => {
      state.loggingSuiteMode = action.payload;
    },
    updateSkipCount: (state, action: PayloadAction<number>) => {
      state.skipCount = action.payload;
    },
    clearLoggingData: (state) => {
      return initialState;
    },
    updateDisplayColumn: (
      state,
      action: PayloadAction<typeof CONFIG.DEFAULT_DISPLAY_COLUMN>
    ) => {
      state.displayColumn = action.payload;
    },
    updateOffsetY: (
      state,
      action: PayloadAction<number | { offsetY: number; targetDepth: number }>
    ) => {
      if (typeof action.payload === "number") {
        state.offsetY = action.payload;
      } else {
        state.offsetY = action.payload.offsetY;
        state.targetDepth = action.payload.targetDepth;
      }
    },
    updateHasShownMeasureInstructions: (
      state,
      action: PayloadAction<boolean>
    ) => {
      state.hasShownMeasureInstructions = action.payload;
    },
    updateOpenModalGeotechData: (state, action: PayloadAction<boolean>) => {
      if (!action.payload) {
        state.measurePointsInterval = {};
      }
      state.openModalGeotechData = action.payload;
    },
    updateCalculateRecoveryBySelectPoint: (
      state,
      action: PayloadAction<{
        imageCropId: number;
        x: number;
        depth: number;
      }>
    ) => {
      state.imageCropId = action.payload.imageCropId;
      state.x = action.payload.x;
      state.depth = action.payload.depth;
    },
    updateLoggingSuiteId: (state, action: PayloadAction<number>) => {
      state.loggingSuiteId = action.payload;
    },
    updateGeotechData: (state, action: PayloadAction<any[]>) => {
      state.geotechData = action.payload;
    },
    updateGeotechDataToEdit: (state, action: PayloadAction<any>) => {
      state.geotechDataToEdit = action.payload;
    },
    updateRefetchGeotechData: (state) => {
      state.refetchGeotechData = !state.refetchGeotechData;
    },
    updateStartPointLineInterval: (
      state,
      action: PayloadAction<{
        x: number;
        rowIndex: number;
      } | null>
    ) => {
      state.startPointLineInterval = action.payload;
    },
    updateMeasurePointsInterval: (
      state,
      action: PayloadAction<{
        start?: { x: number; depth: number; imageCropId?: string };
        end?: { x: number; depth: number; imageCropId?: string };
      }>
    ) => {
      state.measurePointsInterval = action.payload;
    },
    updateMeasureXPointsInterval: (
      state,
      action: PayloadAction<{
        start?: { x: number; depth: number };
        end?: { x: number; depth: number };
      }>
    ) => {
      state.measureXPointsInterval = action.payload;
    },
    updateLoadingGeotechData: (state, action: PayloadAction<boolean>) => {
      state.loadingGeotechData = action.payload;
    },
    updateRecoveries: (state, action: PayloadAction<Recovery[]>) => {
      state.recoveries = action.payload;
    },
    updateTrayDepths: (
      state,
      action: PayloadAction<
        Array<{
          drillHoleId: number;
          trayNumber: number;
          startDepth: number;
          endDepth: number;
          id: number;
        }>
      >
    ) => {
      state.trayDepths = action.payload;
    },
    updateToggleUpdateOCR: (state) => {
      state.toggleUpdateOCR = !state.toggleUpdateOCR;
    },
    updateIsSplitLine: (state, action: PayloadAction<boolean>) => {
      state.isSplitLine = action.payload;

      state.isMergeLine = false;
      state.isDeleteLine = false;
      state.isAddLine = false;
    },
    updateIsMergeLine: (state, action: PayloadAction<boolean>) => {
      state.isMergeLine = action.payload;
      // Reset selectedLine when toggling merge mode
      state.isSplitLine = false;
      state.isDeleteLine = false;
      state.isAddLine = false;

      if (!action.payload) {
        state.selectedLine = null;
      }
    },
    updateIsDeleteLine: (state, action: PayloadAction<boolean>) => {
      state.isDeleteLine = action.payload;
      // Reset selectedLine when toggling delete mode
      if (action.payload) {
        state.isSplitLine = false;
        state.isMergeLine = false;
        state.isAddLine = false;
      } else {
        state.selectedLine = null;
      }
    },
    updateIsAddLine: (state, action: PayloadAction<boolean>) => {
      state.isAddLine = action.payload;
      // When enabling Add mode, disable other modes to ensure
      // only one mode is active at a time
      if (action.payload) {
        state.isSplitLine = false;
        state.isMergeLine = false;
        state.isDeleteLine = false;
        state.selectedLine = null;
      }
      // No need to reset selectedLine as Add mode doesn't use it
    },
    updateSelectedLine: (state, action: PayloadAction<any>) => {
      state.selectedLine = action.payload;
    },
    updateSelectedRockLineType: (
      state,
      action: PayloadAction<RockLineType>
    ) => {
      state.selectedRockLineType = action.payload;
    },
    updateLoggingSuiteSelected: (state, action: PayloadAction<any>) => {
      state.geotechData = [];
      state.loggingSuiteSelected = action.payload;
    },
    updateGeotechSelected: (state, action: PayloadAction<any>) => {
      state.geotechSelected = action.payload;
    },
    updateAllLoggingsTable: (state, action: PayloadAction<any[]>) => {
      state.allLoggingsTable = action.payload;
    },
    resetInfiniteScrollState: (state) => {
      state.infiniteScrollState = {
        currentPage: 0,
        totalItems: 0,
        hasMore: true,
        loading: false,
      };
      state.allLoggings = [];
    },
    updateInfiniteScrollState: (
      state,
      action: PayloadAction<Partial<typeof state.infiniteScrollState>>
    ) => {
      state.infiniteScrollState = {
        ...state.infiniteScrollState,
        ...action.payload,
      };
    },
  },

  extraReducers: (builder) => {
    builder
      .addCase(getGeologyData.pending, (state, action) => {
        state.getGeoDataStatus = RequestState.pending;
      })
      .addCase(getGeologyData.fulfilled, (state, action) => {
        state.getGeoDataStatus = RequestState.success;
        let loggingDatas = action.payload?.items ?? [];

        state.allLoggings = loggingDatas;
        state.reviewDepthTo = undefined;
        const loggingLines: LoggingLine[] = (state.allLoggings as any)
          .map((logging) => {
            const findImageRow = state.imageRows.find(
              (imageRow) =>
                logging?.depthTo >= imageRow.depthFrom &&
                logging?.depthTo <= imageRow.depthTo
            );
            if (!findImageRow) return;
            return {
              id: findImageRow?.id,
              isSave: true,
              data: {
                x:
                  ((logging?.depthTo - findImageRow.depthFrom) /
                    (findImageRow.depthTo - findImageRow.depthFrom)) *
                  findImageRow.coordinate.Width,
                id: findImageRow.id,
              },
            } as LoggingLine;
          })
          .filter(Boolean);
        state.lines = loggingLines;
      })

      .addCase(getExtraViewModeImages.pending, (state, action) => {
        state.getExtraRowStatus = RequestState.pending;
      })
      .addCase(getExtraViewModeImages.fulfilled, (state, action) => {
        state.getExtraRowStatus = RequestState.success;
        state.imageExtraRows = (action.payload?.items?.[0]?.croppedImages ?? [])
          ?.map((item: any) => {
            return {
              ...item,
              coordinate: JSON.parse(item.coordinate),
            };
          })
          .filter((image) => image?.type?.toLowerCase() === "row");
      })
      .addCase(getLoggingInfo.pending, (state, action) => {
        state.getLoggingInfoStatus = RequestState.pending;
      })
      .addCase(getLoggingInfo.fulfilled, (state, action) => {
        state.getLoggingInfoStatus = RequestState.success;
        state.allLoggings = action.payload?.items ?? [];
      })
      .addCase(getLoggingInfoInfinite.pending, (state, action) => {
        if (!state.infiniteScrollState) {
          state.infiniteScrollState = {
            currentPage: 0,
            totalItems: 0,
            hasMore: true,
            loading: false,
          };
        }
        state.infiniteScrollState.loading = true;
      })
      .addCase(getLoggingInfoInfinite.fulfilled, (state, action) => {
        if (!state.infiniteScrollState) {
          state.infiniteScrollState = {
            currentPage: 0,
            totalItems: 0,
            hasMore: true,
            loading: false,
          };
        }

        const { items = [], pagination, reset } = action.payload || {};

        if (reset) {
          // Reset data for initial load
          state.allLoggings = items;
          state.infiniteScrollState.currentPage = 0;
        } else {
          // Append data for infinite scroll
          state.allLoggings = [...(state.allLoggings || []), ...items];
          state.infiniteScrollState.currentPage += 1;
        }

        state.infiniteScrollState.totalItems = pagination?.total ?? 0;
        state.infiniteScrollState.hasMore =
          (state.allLoggings || []).length < (pagination?.total ?? 0);
        state.infiniteScrollState.loading = false;
      })
      .addCase(getLoggingInfoInfinite.rejected, (state, action) => {
        if (!state.infiniteScrollState) {
          state.infiniteScrollState = {
            currentPage: 0,
            totalItems: 0,
            hasMore: true,
            loading: false,
          };
        }
        state.infiniteScrollState.loading = false;
      });
  },
});

export interface AccountSettingsSliceState {
  allLoggings: any[];
  allLoggingsTable: any[];
  previousLoggingDepth?: number;
  reviewDepthFrom: number | null;
  reviewDepthTo?: number | null;
  lines: LoggingLine[];
  depthFrom: number;
  depthTo: number;
  infiniteScrollState: {
    currentPage: number;
    totalItems: number;
    hasMore: boolean;
    loading: boolean;
  };
  contextMenu: LoggingContextMenu;
  hasShownMeasureInstructions: boolean;
  previewImage: PreviewImage;
  getGeoDataStatus?: RequestState;
  imageRows: any[];
  getExtraRowStatus?: RequestState;
  imageExtraRows: any[];
  extraViews: EnumLoggingExtraViews[];
  selectedDrillHole?: { value: number; label: string };
  loggingViewMode: EnumLoggingViews;

  segmentations: SegmentationResult[];
  isShowSegmentation: boolean;
  isShowPercentageRecovery: boolean;
  getLoggingInfoStatus?: RequestState;
  calculateClickedDepth: EnumCalculateClickedDepth;
  imageGap: number;
  selectedSuite?: string;
  refetchLoggingView: boolean;
  skipCount: number;
  displayColumn: {
    fontSize: number;
    color: string;
    strokeWidth: number;
    lineColor: string;
    showText: boolean;
  };
  offsetY?: number;
  targetDepth?: number;
  loggingSuiteMode: "Geology" | "Geotech" | null;
  openModalGeotechData: boolean;
  imageCropId?: number;
  x?: number;
  depth?: number;
  loggingSuiteId?: number;
  geotechData: any[];
  geotechDataToEdit: any;
  refetchGeotechData: boolean;
  startPointLineInterval: {
    x: number;
    rowIndex: number;
  } | null;
  measurePointsInterval: {
    start?: { x: number; depth: number; imageCropId?: string };
    end?: { x: number; depth: number; imageCropId?: string };
  };
  measureXPointsInterval: {
    start?: { x: number; depth: number; imageCropId?: string };
    end?: { x: number; depth: number; imageCropId?: string };
  };
  loadingGeotechData: boolean;
  recoveries: Recovery[];
  trayDepths: Array<{
    drillHoleId: number;
    trayNumber: number;
    startDepth: number;
    endDepth: number;
    id: number;
  }>;
  toggleUpdateOCR: boolean;
  selectedRockLineType: RockLineType;
  isSplitLine: boolean;
  isMergeLine: boolean;
  isDeleteLine: boolean;
  selectedLine: any;
  isAddLine: boolean;
  loggingSuiteSelected: any;
  geotechSelected: any;
  segmentationsDetail: SegmentationResult[];
}

export const {
  updateDisplayColumn,
  updateOffsetY,
  initLogging,
  updateCurrentDepth,
  updateReviewDepthFrom,
  updateSelectedDrilhole,
  updateReviewDepthTo,
  addLoggingLine,
  updateLoggingLine,
  saveLoggingLine,
  saveLoggingData,
  updateLoggingContextMenu,
  updatePreviewImageUrl,
  setImageRows,
  updatePreviousLoggingDepth,
  setImageHyperRows,
  updateExtraViews,
  updateLoggingViewMode,
  updateSegmentsResult,
  updateIsShowSegmentation,
  updateIsShowPercentageRecovery,
  updateCalculateClickedDepth,

  updateImageGap,
  updateSelectedSuite,
  updateRefetchLoggingView,
  updateSkipCount,
  clearLoggingData,
  updateHasShownMeasureInstructions,
  updateLoggingSuiteMode,
  updateOpenModalGeotechData,
  updateCalculateRecoveryBySelectPoint,
  updateLoggingSuiteId,
  updateGeotechData,
  updateGeotechDataToEdit,
  updateRefetchGeotechData,
  updateMeasurePointsInterval,
  updateMeasureXPointsInterval,
  updateLoadingGeotechData,
  updateRecoveries,
  updateTrayDepths,
  updateToggleUpdateOCR,
  updateIsSplitLine,
  updateIsMergeLine,
  updateIsDeleteLine,
  updateIsAddLine,
  updateSelectedLine,
  updateSelectedRockLineType,
  updateLoggingSuiteSelected,
  updateGeotechSelected,
  updateAllLoggingsTable,
  updateSegmentsDetailResult,
  resetInfiniteScrollState,
  updateInfiniteScrollState,
} = loggingSlice.actions;
