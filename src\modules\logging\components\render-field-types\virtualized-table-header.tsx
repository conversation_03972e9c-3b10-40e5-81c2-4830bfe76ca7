import React, { memo, useMemo, useRef, useLayoutEffect, useState } from "react";
import { Tooltip } from "antd";

interface VirtualizedTableHeaderProps {
  columns: any[];
  fontSize: number;
  totalWidth?: number;
  disableFixedPositioning?: boolean; // New prop to disable sticky positioning
}

// Custom hook to detect text overflow
const useTextOverflow = (text: string, dependencies: any[] = []) => {
  const textRef = useRef<HTMLDivElement>(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useLayoutEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        const element = textRef.current;
        const isTextOverflowing = element.scrollWidth > element.clientWidth;
        setIsOverflowing(isTextOverflowing);
      }
    };

    checkOverflow();

    // Add resize observer to handle dynamic width changes
    const resizeObserver = new ResizeObserver(checkOverflow);
    if (textRef.current) {
      resizeObserver.observe(textRef.current);
    }

    return () => {
      resizeObserver.disconnect();
    };
  }, [text, ...dependencies]);

  return { textRef, isOverflowing };
};

// Header cell component with tooltip support
interface HeaderCellProps {
  column: any;
  index: number;
  columnInfo: {
    left: number;
    isFixed: boolean;
    isLast: boolean;
  };
  fontSize: number;
  disableFixedPositioning?: boolean;
}

const HeaderCell = memo<HeaderCellProps>(
  ({ column, index, columnInfo, fontSize, disableFixedPositioning }) => {
    const { textRef, isOverflowing } = useTextOverflow(column.title, [
      column.title,
      column.width,
      fontSize,
    ]);
    const isFixed = columnInfo.isFixed && !disableFixedPositioning;
    const isLastFixed = columnInfo.isLast && !disableFixedPositioning;

    const cellContent = (
      <div
        key={column.key || index}
        className={`flex-shrink-0 px-2 py-3 border-r border-gray-200 font-semibold text-gray-700 bg-gray-50 ${
          isFixed ? "fixed-column-header" : ""
        } ${isLastFixed ? "last-fixed-column" : ""}`}
        style={{
          width: column.width || 150,
          minWidth: column.width || 150,
          ...(isFixed && {
            position: "sticky",
            left: columnInfo.left,
            zIndex: 20,
            backgroundColor: "#f9fafb", // Ensure background color for fixed headers
          }),
        }}
      >
        <div
          ref={textRef}
          className="truncate"
          style={{
            fontSize: `${fontSize}px`,
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
          }}
        >
          {column.title}
        </div>
      </div>
    );

    // Only wrap with tooltip if text is overflowing
    if (isOverflowing && column.title) {
      return (
        <Tooltip
          title={
            <div
              style={{
                fontSize: "14px",
                lineHeight: "1.4",
                maxWidth: "300px",
                wordWrap: "break-word",
              }}
            >
              {column.title}
            </div>
          }
          placement="topLeft"
          mouseEnterDelay={0.3}
          mouseLeaveDelay={0.1}
          zIndex={1060} // Higher than table elements
          getPopupContainer={(triggerNode) => {
            // Try to find the table container to ensure tooltip stays within bounds
            const tableContainer =
              triggerNode.closest(".logging-grid") ||
              triggerNode.closest(".ant-table-wrapper") ||
              triggerNode.closest(".virtualized-table-header") ||
              document.body;
            return tableContainer as HTMLElement;
          }}
        >
          {cellContent}
        </Tooltip>
      );
    }

    return cellContent;
  }
);

HeaderCell.displayName = "HeaderCell";

// Custom virtualized table header component
export const VirtualizedTableHeader = memo<VirtualizedTableHeaderProps>(
  ({ columns, fontSize, totalWidth, disableFixedPositioning = false }) => {
    // Calculate cumulative left positions for fixed columns
    const fixedColumnsInfo = useMemo(() => {
      let cumulativeLeft = 0;
      const fixedInfo: Array<{
        left: number;
        isFixed: boolean;
        isLast: boolean;
      }> = [];

      // Count fixed columns and calculate positions
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        const isFixed = column.fixed === "left";

        if (isFixed) {
          fixedInfo.push({
            left: cumulativeLeft,
            isFixed: true,
            isLast:
              i === 2 ||
              (i < columns.length - 1 && columns[i + 1].fixed !== "left"), // Last fixed column
          });
          cumulativeLeft += column.width || 150;
        } else {
          fixedInfo.push({
            left: 0,
            isFixed: false,
            isLast: false,
          });
        }
      }

      return fixedInfo;
    }, [columns]);

    return (
      <div
        className="flex bg-gray-50 border-b border-gray-200 sticky top-0 z-10"
        style={{
          fontSize: `${fontSize}px`,
          minWidth: totalWidth || "auto",
          width: totalWidth || "auto",
        }}
      >
        {columns.map((column, index) => {
          const columnInfo = fixedColumnsInfo[index];

          return (
            <HeaderCell
              key={column.key || index}
              column={column}
              index={index}
              columnInfo={columnInfo}
              fontSize={fontSize}
              disableFixedPositioning={disableFixedPositioning}
            />
          );
        })}
      </div>
    );
  }
);

VirtualizedTableHeader.displayName = "VirtualizedTableHeader";
