import { Line, Group, Text } from "react-konva";
import { DisplayColumnConfig } from "../types/logging.types";
import { CONFIG } from "../constants/logging.constants";

interface GeophysicsDataProps {
  info: {
    startX: number;
    width: number;
    data: any[];
    name: string;
    columnClass?: number;
    geologySuite?: any;
    geologySuiteField?: any;
    assaySuite?: any;
    assayAttribute?: any;
    suite?: {
      name: string;
      id: number;
    };
    attribute: {
      name: string;
      code: string;
      minInterval: number;
      minValue: number;
      maxValue: number;
      textColor: string;
      backgroundColor: string;
      isActive: boolean;
      id: number;
    };
    loggingViewId?: number;
    numberRange?: any;
    sequence?: number;
    widthFactor?: number;
    type?: string;
    id?: number;
  };
  displayColumn: DisplayColumnConfig;
}

/**
 * Normalizes geophysics data points to fit within the display width
 * @param data - Array of data points with x (depth) and y values
 * @returns Array of data points with original values and normalized y values (0-100)
 */
const normalizeData = (data: any[]) => {
  // Find the range of y values
  const maxValue = Math.max(...data.map((point) => point.y));
  const minValue = Math.min(...data.map((point) => point.y));

  // Normalize y values to 0-100 range while preserving original values
  const normalizedData = data.map((point) => ({
    x: point.x,
    y: point.y, // Keep original value for display
    normalizedY: ((point.y - minValue) / (maxValue - minValue)) * 100, // Scale to 0-100%
  }));
  return normalizedData;
};

export const GeophysicsData = ({
  info,
  displayColumn,
}: GeophysicsDataProps) => {
  // Get first data point to extract available keys
  const data = info?.data?.length ? info?.data[0] : undefined;
  if (!data) return null;

  // Filter out non-geophysics data keys
  const key = info?.attribute?.name;

  // Process and sort data points by depth
  const dataPoints = info.data
    .reduce((acc: any[], item: any) => {
      const x = Number(item["Depth (m)"]);
      const y = Number(item[key]);
      // Handle duplicate depths by keeping latest value
      const index = acc.findIndex((point) => point.x === x);
      if (index !== -1) {
        acc[index] = { x, y }; // Replace with latest value
      } else {
        acc.push({ x, y });
      }
      return acc;
    }, [])
    .filter((point) => !isNaN(point.y) && !isNaN(point.x))
    .sort((a: any, b: any) => a.x - b.x); // Sort by depth

  // Normalize data points to fit within display width
  const normalizedDataPoints = normalizeData(dataPoints);

  return (
    <Group>
      {/* Draw geophysics line using normalized values for positioning */}
      <Line
        points={normalizedDataPoints.flatMap((point: any) => [
          info.startX + (point.normalizedY * info.width) / 100,
          point.x * CONFIG.SCALE,
        ])}
        stroke={displayColumn.lineColor}
        strokeWidth={displayColumn.strokeWidth}
        lineJoin="round"
        lineCap="round"
        tension={0.05}
      />
      {/* Display original values as text labels using normalized positions */}
      {displayColumn.showText &&
        normalizedDataPoints.map((point: any) => (
          <Text
            key={`${point.x}-${point.y}`}
            text={point.y.toFixed(2)}
            x={info.startX + (point.normalizedY * info.width) / 100}
            y={point.x * CONFIG.SCALE}
            fontSize={displayColumn.fontSize}
            fill={displayColumn.color}
            align="center"
          />
        ))}
    </Group>
  );
};
