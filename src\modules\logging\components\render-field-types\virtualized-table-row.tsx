import React, { memo, useMemo } from "react";
import { Tooltip } from "antd";

// Types
interface LoggingRowData {
  id: string;
  valueId: string;
  depthFrom: number;
  depthTo: number;
  rowStatus: any;
  geologySuiteId: number;
  drillholeId: number;
  loading?: boolean;
  errorDetail?: any;
  dataEntryValues: any[];
}

interface VirtualizedTableRowProps {
  record: LoggingRowData;
  index: number;
  columns: any[];
  style: React.CSSProperties;
  fontSize: number;
  totalWidth?: number;
}

// Helper function to extract text content for tooltips
const getTextContent = (content: React.ReactNode): string => {
  if (typeof content === "string") return content;
  if (typeof content === "number") return content.toString();
  if (React.isValidElement(content) && content.props.children) {
    return getTextContent(content.props.children);
  }
  return "";
};

// Custom virtualized table row component
export const VirtualizedTableRow = memo<VirtualizedTableRowProps>(
  ({ record, index, columns, style, fontSize, totalWidth }) => {
    // Calculate the total width for the row to ensure proper horizontal scrolling
    const calculatedTotalWidth =
      totalWidth || columns.reduce((sum, col) => sum + (col.width || 150), 0);

    // Calculate cumulative left positions for fixed columns
    const fixedColumnsInfo = useMemo(() => {
      let cumulativeLeft = 0;
      const fixedInfo: Array<{
        left: number;
        isFixed: boolean;
        isLast: boolean;
      }> = [];

      // Count fixed columns and calculate positions
      for (let i = 0; i < columns.length; i++) {
        const column = columns[i];
        const isFixed = column.fixed === "left";

        if (isFixed) {
          fixedInfo.push({
            left: cumulativeLeft,
            isFixed: true,
            isLast:
              i === 2 ||
              (i < columns.length - 1 && columns[i + 1].fixed !== "left"), // Last fixed column
          });
          cumulativeLeft += column.width || 150;
        } else {
          fixedInfo.push({
            left: 0,
            isFixed: false,
            isLast: false,
          });
        }
      }

      return fixedInfo;
    }, [columns]);

    return (
      <div
        style={{
          ...style,
          minWidth: calculatedTotalWidth,
          width: calculatedTotalWidth,
          height: 60, // Fixed height for virtualized table
          maxHeight: 60, // Prevent height expansion
        }}
        className={`flex border-b border-gray-200 hover:bg-gray-50 ${
          record.loading ? "opacity-60" : ""
        }`}
      >
        {columns.map((column, colIndex) => {
          let cellContent: React.ReactNode;

          // Handle different column types
          if (column.render) {
            try {
              // For columns with custom render functions
              const dataValue = column.dataIndex
                ? record[column.dataIndex]
                : undefined;
              cellContent = column.render(dataValue, record, index);
            } catch (error) {
              cellContent = <span className="text-red-500">Error</span>;
            }
          } else if (column.dataIndex) {
            // For simple data columns
            cellContent = record[column.dataIndex];
          } else {
            // Fallback for columns without dataIndex
            cellContent = `Col ${colIndex}`;
          }

          const columnInfo = fixedColumnsInfo[colIndex];
          const isFixed = columnInfo.isFixed;
          const isLastFixed = columnInfo.isLast;

          // Extract text content for tooltip
          const textContent = getTextContent(cellContent);
          const shouldShowTooltip = textContent && textContent.length > 20; // Show tooltip for longer content

          const cellElement = (
            <div
              key={column.key || colIndex}
              className={`flex-shrink-0 px-1 py-1 border-r border-gray-200 virtualized-table-row-cell ${
                isFixed ? "fixed-column" : ""
              } ${isLastFixed ? "last-fixed-column" : ""}`}
              style={{
                width: column.width || 150,
                minWidth: column.width || 150,
                maxWidth: column.width || 150,
                fontSize: `${fontSize}px`,
                display: "flex",
                alignItems: "center", // Center alignment for consistent appearance
                height: 60, // Fixed cell height
                maxHeight: 60, // Prevent cell expansion
                position: "relative",
                overflow: "hidden", // Strict overflow control
                boxSizing: "border-box",
                left: isFixed ? columnInfo.left : 0,
              }}
            >
              <div className="virtualized-cell-content">{cellContent}</div>
            </div>
          );

          // Wrap with tooltip if content is long
          return shouldShowTooltip ? (
            <Tooltip
              key={column.key || colIndex}
              title={textContent}
              placement="topLeft"
              mouseEnterDelay={0.5}
            >
              {cellElement}
            </Tooltip>
          ) : (
            cellElement
          );
        })}
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.record === nextProps.record &&
      prevProps.index === nextProps.index &&
      prevProps.fontSize === nextProps.fontSize &&
      prevProps.columns === nextProps.columns
    );
  }
);

VirtualizedTableRow.displayName = "VirtualizedTableRow";
