import { SortOrder } from "@/common/interfaces/general/general.types";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { InputNumberCommon } from "@/components/common/input-number";
import { InputTextCommon } from "@/components/common/input-text";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetListProject } from "@/modules/projects/hooks";
import { useGetListProspect } from "@/modules/prospect/hooks/useGetListProspect";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, Spin } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateDrillHole } from "../hooks/useCreateDrillHole.hook";
import { useDeleteDrillHole } from "../hooks/useDeleteDrillHole.hook";
import { useUpdateDrillHole } from "../hooks/useUpdateDrillHole.hook";
import { drillHoleStatusOptions } from "../model/enum/drillhole.enum";
import {
  DrillHoleBody,
  DrillHoleBodyType,
} from "../model/schema/drillhole.schema";

export interface IModalCompanyProps {
  modalState: {
    isOpen: boolean;
    detailInfo: any;
    type: string;
  };
  setModalState: (value: any) => void;
  fetchListDrillhole: (params?: any) => void;
}

export function ModalDrillhole(props: IModalCompanyProps) {
  const { modalState, setModalState, fetchListDrillhole } = props;
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user?.userInfo?.prospectId
  );
  const { request: requestCreateDrillHole, loading: loadingCreateDrillhole } =
    useCreateDrillHole();
  const { request: requestDeleteDrillHole, loading: loadingDeleteDrillhole } =
    useDeleteDrillHole();
  const { request: requestUpdateDrillHole, loading: loadingUpdateDrillhole } =
    useUpdateDrillHole();
  const handleCancel = () => {
    setModalState({ ...modalState, isOpen: false });
  };
  const { control, handleSubmit, watch, setValue, reset, getValues } =
    useForm<DrillHoleBodyType>({
      resolver: zodResolver(DrillHoleBody),
      defaultValues: {
        name: modalState.detailInfo?.name,
        prospectId: modalState.detailInfo?.prospect?.id,
        rl: modalState.detailInfo?.rl,
        maxDepth: modalState.detailInfo?.maxDepth,
        projectId: modalState.detailInfo?.project?.id,
        drillHoleStatus: modalState.detailInfo?.drillHoleStatus,
        isActive: modalState.detailInfo?.isActive ?? true,
        elevation: modalState.detailInfo?.elevation,
        northing: modalState.detailInfo?.northing,
        easting: modalState.detailInfo?.easting,
        dip: modalState.detailInfo?.dip,
        azimuth: modalState.detailInfo?.azimuth,
      },
    });

  const selectedProjectId = watch("projectId");

  const isConfirm = modalState.type === "delete";

  const onSubmit = (values: DrillHoleBodyType) => {
    if (watch("prospectId") === undefined) {
      toast.error("Prospect is required");
      return;
    }
    if (modalState.type === "create") {
      requestCreateDrillHole(
        {
          ...values,
          name: values.name.toUpperCase(),
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Create drillhole successfully");
          fetchListDrillhole();
        },
        (error) => {
          toast.error(error);
        }
      );
    }
    if (modalState.type === "update") {
      requestUpdateDrillHole(
        {
          ...(values as any),
          name: values.name.toUpperCase(),
          id: modalState.detailInfo.id,
        },
        () => {
          setModalState({ ...modalState, isOpen: false });
          toast.success("Update drillhole successfully");
          fetchListDrillhole();
        },
        (error) => {
          console.log("error: ", error);
          toast.error(error);
        }
      );
    }
  };

  const handleDelete = () => {
    requestDeleteDrillHole(
      {
        id: modalState.detailInfo.id,
      },
      () => {
        setModalState({ ...modalState, isOpen: false });
        fetchListDrillhole();
      },
      (error) => {
        toast.error(error);
      }
    );
  };
  const {
    data: projects,
    request: getListProjects,
    loading: loadingListProject,
  } = useGetListProject();

  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const [keywordProject, setKeywordProject] = useState<any>();
  // set default value for projectId
  useEffect(() => {
    setValue("projectId", globalProjectId as any);
  }, [globalProjectId]);

  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  useEffect(() => {
    if (!modalState.isOpen) return;
    getListProjects({
      maxResultCount: maxResultCountProject,
      skipCount: 0,
      sortField: "name",
      sortOrder: SortOrder.ASC,
      keyword: keywordProject,
    });
  }, [maxResultCountProject, modalState.isOpen, keywordProject]);
  // set default value for projectId
  useEffect(() => {
    if (modalState.type === "update") return;
    setValue("prospectId", globalProspectId as any);
  }, [globalProspectId]);

  const {
    data: prospects,
    request: getListProspects,
    loading: loadingListProspect,
  } = useGetListProspect();
  const [maxResultCountProspect, setMaxResultCountProspect] = useState(10);
  const [keywordProspect, setKeywordProspect] = useState<any>();
  useEffect(() => {
    if (!selectedProjectId) return;
    getListProspects({
      maxResultCount: maxResultCountProspect,
      skipCount: 0,
      projectIds: selectedProjectId ? [selectedProjectId] : undefined,
      keyword: keywordProspect,
    });
  }, [
    maxResultCountProspect,
    modalState.isOpen,
    keywordProspect,
    selectedProjectId,
  ]);

  const handleScrollProspect = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProspect(maxResultCountProspect + 10);
    }
  };
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={handleCancel}
      style={{ borderRadius: 8 }}
      width={600}
      closable={false}
    >
      {isConfirm ? (
        <div className="flex flex-col gap-2">
          <p className="font-bold text-16-18 capitalize font-visby">
            Are you sure you want to delete this drillhole?
          </p>
          <p>
            This action cannot be undone. This will permanently delete the
            drillhole
          </p>
          <div className="flex justify-end gap-2">
            <ButtonCommon onClick={handleCancel} className="btn btn-sm">
              No
            </ButtonCommon>
            <ButtonCommon
              loading={loadingDeleteDrillhole}
              onClick={handleDelete}
              className="btn btn-sm bg-primary text-white hover:bg-primary"
            >
              Yes
            </ButtonCommon>
          </div>
        </div>
      ) : (
        <div className="px-6 flex flex-col gap-4">
          <p className="font-bold text-24-28 capitalize text-center font-visby">
            {modalState.type === "update"
              ? "Update drillhole"
              : "Create drillhole"}
          </p>
          <Form
            onFinish={handleSubmit(onSubmit, (err) => {
              console.log("err: ", err);
            })}
            className="flex flex-col gap-3"
          >
            <InputTextCommon
              label="Name"
              isRequired
              name="name"
              placeholder="Type drillhole name here"
              control={control}
              onChange={(e) => {
                setValue("name", e.target.value.toUpperCase());
              }}
            />
            <SelectCommon
              control={control}
              label="Project"
              name="projectId"
              isRequired
              onSearch={(value) => setKeywordProject(value)}
              onChange={(value) => {
                getListProspects({
                  maxResultCount: maxResultCountProspect,
                  skipCount: 0,
                  projectIds: value ? [value] : undefined,
                  sortField: "name",
                  sortOrder: SortOrder.ASC,
                });
                setValue("prospectId", undefined);
              }}
              searchValue={keywordProject}
              onBlur={() => {
                setKeywordProject("");
                getListProjects({
                  maxResultCount: maxResultCountProject,
                  skipCount: 0,
                  sortField: "name",
                  sortOrder: SortOrder.ASC,
                });
              }}
              options={projects.map((project) => ({
                label: project.name,
                value: project.id,
              }))}
              notFoundContent={
                loadingListProject ? <Spin size="small" /> : <>Not found</>
              }
              filterOption={false} // disable client-side filtering
              showSearch
              placeholder="Select project"
              onPopupScroll={handleScrollProject}
            />
            <SelectCommon
              isRequired
              control={control}
              label="Prospect"
              name="prospectId"
              onSearch={(value) => {
                setKeywordProspect(value);
              }}
              searchValue={keywordProspect}
              onBlur={() => {
                setKeywordProspect("");
                getListProspects({
                  maxResultCount: maxResultCountProspect,
                  skipCount: 0,
                  sortField: "name",
                  sortOrder: SortOrder.ASC,
                  projectIds: selectedProjectId
                    ? [selectedProjectId]
                    : undefined,
                });
              }}
              options={prospects.map((prospect) => ({
                label: prospect.name,
                value: prospect.id,
              }))}
              notFoundContent={
                loadingListProspect ? <Spin size="small" /> : <>Not found</>
              }
              filterOption={false} // disable client-side filtering
              showSearch
              placeholder="Select prospect"
              onPopupScroll={handleScrollProspect}
            />

            <InputNumberCommon
              label="Maximum Depth"
              name="maxDepth"
              placeholder="Type maximum depth here (decimal)"
              control={control}
            />
            <div className="flex gap-2">
              <InputNumberCommon
                label="Elevation"
                name="elevation"
                placeholder="Elevation "
                control={control}
              />
              <InputNumberCommon
                label="Northing"
                name="northing"
                placeholder="Northing"
                control={control}
              />
              <InputNumberCommon
                label="Easting"
                name="easting"
                placeholder="Easting"
                control={control}
              />
              <InputNumberCommon
                label="Dip"
                name="dip"
                placeholder="Dip"
                control={control}
              />
              <InputNumberCommon
                label="Azimuth"
                name="azimuth"
                placeholder="Azimuth"
                control={control}
              />
            </div>

            <SelectCommon
              control={control}
              label="Status"
              name="drillHoleStatus"
              options={drillHoleStatusOptions}
              placeholder="Select drillhole status"
            />
            <ToogleCommon label="Active" name="isActive" control={control} />
            <div className="flex flex-col gap-3 mt-3">
              <ButtonCommon
                loading={loadingCreateDrillhole || loadingUpdateDrillhole}
                type="submit"
                className="btn btn-sm w-full hover:bg-primary-hover bg-primary text-white border-none"
              >
                {modalState.type === "update"
                  ? "Update Drillhole"
                  : "Add Drillhole"}
              </ButtonCommon>
              <ButtonCommon
                onClick={handleCancel}
                className="btn py-2 w-full btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
              >
                Cancel
              </ButtonCommon>
            </div>
          </Form>
        </div>
      )}
    </ModalCommon>
  );
}
