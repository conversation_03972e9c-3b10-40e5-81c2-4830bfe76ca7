import { useDebounce } from "@/hooks/useDebounce";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useState } from "react";
import imageSubTypeRequest from "../api/image-subtype.api";

export const useQueryImageSubType = () => {
  const [searchParams, setSearchParams] = useState<any>({
    isActive: true,
    maxResultCount: 10,
    skipCount: 0,
  });
  const debouncedSearch = useDebounce(searchParams?.keyword);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["image-sub-type", { ...searchParams, keyword: debouncedSearch }],
    queryFn: () =>
      imageSubTypeRequest.getList({
        ...searchParams,
        isActive: searchParams?.isActive ?? true,
        keyword: debouncedSearch,
      }),
    placeholderData: keepPreviousData,
  });
  return {
    data,
    isLoading,
    error,
    refetch,
    setSearchParams,
    searchParams,
  };
};
