# Add Rock Line Feature Implementation Plan

Based on my analysis of the codebase and the clarifications provided, I've created a comprehensive implementation plan for adding the "Add rock line" feature.

## Current Understanding

The application currently has three rock line manipulation modes:

1. **Split line mode**: Allows splitting existing lines by clicking on them
2. **Merge line mode**: Allows merging two lines by selecting them sequentially
3. **Delete line mode**: Allows deleting lines by clicking on them

These modes are:

- Controlled via Redux state (`isSplitLine`, `isMergeLine`, `isDeleteLine`)
- Mutually exclusive (only one can be active at a time)
- Visually represented by buttons with corresponding icons
- Implemented through API calls to create, update, or delete rock lines

## New Feature Requirements

The new "Add rock line" feature should:

1. Add a new "Add rock line" button to the right of the "Split rock line" button
2. Store a new mode state (`isAddLine`) in the Redux store
3. Implement the logic to:
   - Collect two points when clicked on the image
   - Display a temporary indicator line while selecting the second point
   - Create a new rock line between those points
   - Refresh the UI after creation
4. Ensure only one mode can be active at a time (delete, split, merge, or add)
5. Keep the Add mode active after successful line creation to allow adding multiple lines

## Implementation Plan

### 1. Update Redux Store

Update `logging.slice.ts` to add:

- A new `isAddLine` state in the initial state (default: false)
- A new `updateIsAddLine` action to toggle the state
- Logic to ensure mode exclusivity

```mermaid
graph TD
    A[Update logging.slice.ts] --> B[Add isAddLine to initialState]
    B --> C[Create updateIsAddLine reducer]
    C --> D[Update mode toggle logic for mutual exclusivity]
```

### 2. Add UI Button to logging-stage.tsx

Add a new button in the logging-stage.tsx component:

- Position it to the right of "Split rock line" button
- Use PlusOutlined from @ant-design/icons for consistency
- Implement toggle behavior that disables other modes

```mermaid
graph TD
    A[Update logging-stage.tsx] --> B[Add new button with PlusOutlined icon]
    B --> C[Add tooltip with "Add rock line" text]
    C --> D[Implement button click handler]
    D --> E[Ensure proper styling and position]
```

### 3. Implement Two-Point Collection Logic

Modify the ImageRowLogging component:

- Add state to track first clicked point
- Enhance handleImageClick function to handle Add mode
- Add visual indicator (temporary line) while selecting the second point
- Validate that both points are on the same image

```mermaid
graph TD
    A[Update ImageRowLogging component] --> B[Add state for tracking first point]
    B --> C[Enhance handleImageClick for Add mode]
    C --> D[Add temporary line visualization]
    D --> E[Add same-image validation]
```

### 4. Implement Line Creation Logic

After collecting two points:

- Call the createRockLine API with appropriate parameters
- Reset the first point state (but keep Add mode active)
- Refresh the UI using the existing refreshImageData function
- Show success/error feedback to the user

```mermaid
graph TD
    A[Implement line creation] --> B[Prepare API parameters]
    B --> C[Call createRockLine API]
    C --> D[Handle success/error responses]
    D --> E[Reset first point state]
    E --> F[Call refreshImageData]
    F --> G[Display success message]
```

## Detailed Code Changes

### 1. Redux Slice Updates (src/modules/logging/redux/loggingSlice/logging.slice.ts)

```typescript
// Add to initialState
const initialState: AccountSettingsSliceState = {
  // existing state...
  isAddLine: false,
  // other existing state...
};

// Add reducer
updateIsAddLine: (state, action: PayloadAction<boolean>) => {
  state.isAddLine = action.payload;
  // Turn off other modes when enabling add line mode
  if (action.payload) {
    state.isSplitLine = false;
    state.isMergeLine = false;
    state.isDeleteLine = false;
  }
},

// Update interface
export interface AccountSettingsSliceState {
  // existing properties...
  isAddLine: boolean;
  // other existing properties...
}

// Add to exports
export const {
  // existing exports...
  updateIsAddLine,
  // other existing exports...
} = loggingSlice.actions;
```

### 2. Button UI (src/modules/logging/components/logging-stage.tsx)

```tsx
// Import PlusOutlined
import {
  LoadingOutlined,
  MergeOutlined,
  DeleteOutlined,
  SplitCellsOutlined,
  PlusOutlined,
} from "@ant-design/icons";

// Add isAddLine to useAppSelector
const isAddLine = useAppSelector((state) => state.logging.isAddLine);

// Add the button after the Split rock line button (around line 582)
{
  isShowSegmentation && (
    <Tooltip title={`Add rock line`} placement="left">
      <div
        onClick={() => {
          dispatch(updateIsAddLine(!isAddLine));
          // Turn off other modes when enabling add line mode
          if (isSplitLine && !isAddLine) {
            dispatch(updateIsSplitLine(false));
          }
          if (isMergeLine && !isAddLine) {
            dispatch(updateIsMergeLine(false));
          }
          if (isDeleteLine && !isAddLine) {
            dispatch(updateIsDeleteLine(false));
          }
        }}
        className={`p-2 flex items-center justify-center border-l border-r border-b border-gray-300 hover:cursor-pointer  ${
          isAddLine
            ? "bg-primary text-white border-primary border-t border-t-white"
            : "bg-white hover:bg-gray-100"
        }`}
      >
        <PlusOutlined width={20} />
      </div>
    </Tooltip>
  );
}
```

### 3. Point Collection (src/modules/logging/components/image-row-logging.tsx)

```tsx
// Add state for tracking first point
const [firstPoint, setFirstPoint] = useState<{
  x: number;
  y: number;
  rowIndex: number;
  imageCropId: number;
} | null>(null);

// Add isAddLine to useAppSelector
const isAddLine = useAppSelector((state) => state.logging.isAddLine);

// Enhance handleImageClick to handle add mode
const handleImageClick = async (e: KonvaEventObject<MouseEvent>) => {
  if (isMergeLine || isSplitLine || isDeleteLine) {
    return;
  }

  // Handle Add Line mode
  if (isAddLine) {
    const stage = e.target.getStage();
    const pointerPos = stage?.getPointerPosition();
    if (!pointerPos || !stage?.scale()?.x || !data?.width) return;
    const clickedX = (pointerPos.x - stage?.x()) / (stage?.scale()?.x ?? 1);
    const clickedY = startY + data.height / 2; // Middle of the image vertically

    if (!firstPoint) {
      // First point selection
      setFirstPoint({
        x: clickedX,
        y: clickedY,
        rowIndex: imageIndex,
        imageCropId: data.id,
      });
      return;
    } else {
      // Second point selection - verify same image
      if (
        firstPoint.rowIndex !== imageIndex ||
        firstPoint.imageCropId !== data.id
      ) {
        toast.error("Both points must be on the same image");
        return;
      }

      // Create the rock line
      try {
        const createParams = {
          type: selectedRockLineType,
          startX: firstPoint.x,
          startY: firstPoint.y - startY, // Adjust for startY offset
          endX: clickedX,
          endY: clickedY - startY, // Adjust for startY offset
          depthFrom: calculateClickDepthStandard(firstPoint.x),
          depthTo: calculateClickDepthStandard(clickedX),
          imageCropId: data.id,
        };

        const result = await recoveryRequest.createRockLine(createParams);

        if (result.state === RequestState.error) {
          toast.error(result.message || "Failed to create rock line");
        } else {
          toast.success("Rock line created successfully");
          // Reset first point but keep Add mode active
          setFirstPoint(null);
          // Refresh UI
          if (refreshImageData) {
            refreshImageData();
          }
        }
      } catch (error) {
        toast.error("Error creating rock line");
      }

      // Reset first point
      setFirstPoint(null);
      return;
    }
  }

  // Original click handling logic...
  // ...
};

// Add temporary line display in the render method
// Inside the return statement, add:
{
  isAddLine && firstPoint && firstPoint.rowIndex === imageIndex && (
    <Line
      points={[
        firstPoint.x,
        firstPoint.y,
        // Draw to current mouse position if available, otherwise just to edge
        mousePosition ? mousePosition.x : data.width,
        firstPoint.y,
      ]}
      stroke="yellow"
      strokeWidth={3}
      dash={[10, 5]} // Dashed line
    />
  );
}

// Add mouse position tracking (add to component)
const [mousePosition, setMousePosition] = useState<{
  x: number;
  y: number;
} | null>(null);

// Add to event handlers
const handleMouseMove = (e: KonvaEventObject<MouseEvent>) => {
  if (isAddLine && firstPoint) {
    const stage = e.target.getStage();
    const pointerPos = stage?.getPointerPosition();
    if (!pointerPos || !stage?.scale()?.x) return;
    const x = (pointerPos.x - stage?.x()) / (stage?.scale()?.x ?? 1);
    const y = startY + data.height / 2;
    setMousePosition({ x, y });
  }
};

// Update the Group component to include the mouse move handler:
<Group
  opacity={uiProps?.opacity ?? 1}
  onContextMenu={handleContextMenu}
  onTouchStart={handleTouchStart}
  onTouchMove={handleTouchMove}
  onTouchEnd={handleTouchEnd}
  onClick={handleImageClick}
  onMouseMove={handleMouseMove}
>
  {/* Existing content */}
</Group>;
```

## Testing Plan

1. **Redux State Testing**: Verify that the new state and reducers work correctly.
2. **UI Testing**: Verify that the new button appears correctly and toggles the mode.
3. **Functionality Testing**:
   - Test creating a line within a single image
   - Verify that trying to create a line across multiple images is prevented
   - Test that the Add mode stays active after line creation
   - Verify that activating other modes deactivates Add mode and vice versa
4. **Edge Cases**:
   - Test with image zoomed in/out
   - Test with very close points
   - Test with points at image edges

## Error Handling and Edge Cases

### 1. Input Validation

**Scenario: Invalid Points**

- **Problem**: User clicks on invalid areas or non-image areas
- **Handling**:
  - Validate click events to ensure they're within the bounds of the image
  - Ignore clicks outside image boundaries
  - Add bounds checking with appropriate feedback: `if (clickedX < 0 || clickedX > data.width) return;`

**Scenario: Same Point Selection**

- **Problem**: User selects the same point twice or points very close together
- **Handling**:
  - Add minimum distance validation between points
  - Code:
    ```typescript
    const MIN_DISTANCE = 5; // Minimum 5px distance
    if (Math.abs(firstPoint.x - clickedX) < MIN_DISTANCE) {
      toast.warning(
        "Points are too close together. Please select a point further away."
      );
      return;
    }
    ```

### 2. API Error Handling

**Scenario: API Failure**

- **Problem**: Network issues or server errors when calling createRockLine
- **Handling**:
  - Implement comprehensive try/catch blocks
  - Show specific error messages based on error types
  - Preserve first point state to allow retry
  - Add timeout handling for API calls
  - Code:
    ```typescript
    try {
      // API call with timeout
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

      const result = await recoveryRequest.createRockLine(createParams);
      clearTimeout(timeoutId);

      // Handle result
    } catch (error) {
      if (error.name === "AbortError") {
        toast.error("Request timed out. Please try again.");
      } else if (error.response?.status === 400) {
        toast.error("Invalid parameters for rock line creation.");
      } else if (
        error.response?.status === 401 ||
        error.response?.status === 403
      ) {
        toast.error("You don't have permission to create rock lines.");
      } else {
        toast.error(
          `Error creating rock line: ${error.message || "Unknown error"}`
        );
      }
      // Don't reset first point to allow retry
    }
    ```

### 3. UI Edge Cases

**Scenario: Zoom/Pan During Selection**

- **Problem**: User zooms or pans after selecting first point
- **Handling**:
  - Store coordinates in a zoom-independent way
  - Update visual indicator when zoom/pan occurs
  - Maintain correct scale/position calculations

**Scenario: Canvas Resizing**

- **Problem**: Window/container resizing during point selection
- **Handling**:
  - Add window resize listener to adjust coordinates
  - Recalculate positions on resize events

**Scenario: Modal Dialogs**

- **Problem**: Modal dialog opens during point selection
- **Handling**:
  - Preserve selection state across modal interactions
  - Add cleanup on modal close

### 4. User Experience Considerations

**Scenario: Accidental Mode Switching**

- **Problem**: User accidentally switches mode while in middle of adding a line
- **Handling**:
  - Add confirmation dialog if first point is selected:
    ```typescript
    if (firstPoint && !isAddLine) {
      // Show confirmation before disabling add mode
      if (
        confirm(
          "You're in the middle of adding a line. Discard and switch modes?"
        )
      ) {
        setFirstPoint(null);
      } else {
        // Revert mode change
        dispatch(updateIsAddLine(true));
      }
    }
    ```

**Scenario: Multiple Rapid Clicks**

- **Problem**: User clicks rapidly creating unwanted lines
- **Handling**:
  - Add debounce to click handling
  - Visual feedback about click registration
  - Code:

    ```typescript
    // At the top of component
    const [isCreatingLine, setIsCreatingLine] = useState(false);

    // In line creation logic
    if (isCreatingLine) {
      toast.info("Line creation in progress, please wait");
      return;
    }

    setIsCreatingLine(true);
    try {
      // API calls
    } finally {
      setIsCreatingLine(false);
    }
    ```

### 5. Recovery Mechanisms

**Scenario: Failed Line Creation**

- **Problem**: Line creation fails but UI needs to recover
- **Handling**:
  - Add retry button in toast notification
  - Keep first point if creation fails
  - Provide option to manually reset/retry

**Scenario: Unexpected API Response Format**

- **Problem**: API returns unexpected format or errors
- **Handling**:
  - Add data validation before using API response
  - Graceful fallbacks for missing/invalid data
  - Code:
    ```typescript
    if (result.state === RequestState.success) {
      // Validate response data
      if (!result.data && typeof result.data !== "number") {
        toast.warning(
          "API returned unexpected data format. The line may not have been created properly."
        );
      } else {
        // Success path
      }
    }
    ```

By handling these error cases and edge scenarios systematically, we ensure a robust implementation that handles unexpected situations gracefully.

## Implementation Timeline

1. Redux store changes: 0.5 hours
2. UI button implementation: 0.5 hours
3. Point collection and line creation logic: 2 hours
4. Error handling implementation: 1 hour
5. Testing and refinements: 1 hour

Total estimated time: 5 hours

## Conclusion

This implementation follows the existing pattern for line manipulation modes, integrating seamlessly with the current codebase. The feature will allow users to easily add new rock lines by clicking two points on an image, with visual feedback during the process.

The Add mode will remain active after line creation to facilitate adding multiple lines consecutively, enhancing workflow efficiency when users need to add several lines.
