export interface EnvConfigType {
  primaryEndPoint: string;
}
const getEnvConfigs: () => EnvConfigType = () => {
  if (process.env.NEXT_PUBLIC_ENV === "staging") {
    return {
      primaryEndPoint: process.env.NEXT_PUBLIC_PRIMARY_ENDPOINT ?? "",
    };
  }

  // For dev
  return {
    primaryEndPoint: process.env.NEXT_PUBLIC_PRIMARY_ENDPOINT ?? "",
  };
};

const appConfigs = {
  logPipes: [console],
  env: getEnvConfigs(),
};

export { appConfigs };
