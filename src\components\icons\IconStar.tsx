import React from "react";

const IconStar = (props: JSX.IntrinsicElements["svg"]) => {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.9455 7.22016L12 4.5L11.0545 7.22016C10.5052 8.80044 9.03042 9.87192 7.35774 9.90601L4.47853 9.96468L6.77337 11.7045C8.10656 12.7153 8.66987 14.449 8.18541 16.0503L7.35149 18.8067L9.7153 17.1618C11.0886 16.2062 12.9115 16.2062 14.2847 17.1618L16.6486 18.8067L15.8146 16.0503C15.3302 14.449 15.8935 12.7153 17.2267 11.7045L19.5215 9.96468L16.6423 9.90601C14.9696 9.87192 13.4949 8.80044 12.9455 7.22016ZM13.8891 3.84334C13.2666 2.05222 10.7335 2.05222 10.1109 3.84334L9.16537 6.5635C8.89071 7.35364 8.15333 7.88938 7.31699 7.90642L4.43778 7.9651C2.54193 8.00373 1.75917 10.4128 3.27024 11.5584L5.56508 13.2983C6.23167 13.8036 6.51333 14.6705 6.2711 15.4712L5.43717 18.2276C4.88807 20.0426 6.93736 21.5315 8.49385 20.4484L10.8577 18.8035C11.5443 18.3257 12.4557 18.3257 13.1424 18.8035L15.5062 20.4484C17.0627 21.5315 19.112 20.0426 18.5629 18.2276L17.7289 15.4712C17.4867 14.6705 17.7684 13.8036 18.435 13.2983L20.7298 11.5584C22.2409 10.4128 21.4581 8.00373 19.5623 7.9651L16.6831 7.90642C15.8467 7.88938 15.1093 7.35364 14.8347 6.5635L13.8891 3.84334Z"
        fill="currentColor"
      />
    </svg>
  );
};

export default IconStar;
