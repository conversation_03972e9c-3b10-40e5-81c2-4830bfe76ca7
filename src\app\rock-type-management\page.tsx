"use client";

import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableRockGroup from "@/modules/rock-groups/components/table-rock-groups";
import TableRockType from "@/modules/rock-type/components/table-rock-type";
import { RockTypesTree } from "@/modules/rock-types-tree";
import { ApartmentOutlined, TableOutlined } from "@ant-design/icons";
import { Tabs } from "antd";
import { useState } from "react";
import { MdOutlineAirlineSeatIndividualSuite } from "react-icons/md";

export default function RockTypeManagementPage() {
  const [activeTab, setActiveTab] = useState("table");

  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <div className="p-4">
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          type="card"
          items={[
            {
              key: "table",
              label: (
                <span className="min-w-[100px] inline-block text-center">
                  <TableOutlined /> Table View
                </span>
              ),
              children: (
                <div className="mt-2">
                  <TableRockType />
                </div>
              ),
            },
            {
              key: "tree",
              label: (
                <span className="min-w-[100px] inline-block text-center">
                  <ApartmentOutlined /> Tree View
                </span>
              ),
              children: (
                <div className="mt-2">
                  <RockTypesTree />
                </div>
              ),
            },
            {
              key: "rock-group",
              label: (
                <span className="min-w-[100px] flex items-center">
                  <MdOutlineAirlineSeatIndividualSuite className="mr-1 text-lg" />{" "}
                  Rock Groups
                </span>
              ),
              children: (
                <div className="mt-2">
                  <TableRockGroup />
                </div>
              ),
            },
          ]}
        />
      </div>
    </PermissionProvider>
  );
}
