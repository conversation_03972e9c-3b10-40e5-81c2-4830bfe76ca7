"use client";
import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import dynamic from "next/dynamic";
import { Suspense } from "react";

// Dynamically import the 3D component with SSR disabled
const DrillholeViewPanel = dynamic(
  () => import("@/modules/threed/components/drillhole-view-panel"),
  { ssr: false }
);

export default function Page() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <div className="h-[calc(100vh_-_146px)]">
        <Suspense
          fallback={<div className="text-blue-500">Loading 3D view...</div>}
        >
          <DrillholeViewPanel />
        </Suspense>
      </div>
    </PermissionProvider>
  );
}
