import z from "zod";

export const GeotechDataBody = z.object({
  id: z.any().optional(),
  geotechSuiteId: z.number().nullable().optional(),
  geotechFieldId: z.number().nullable().optional(),
  drillHoleId: z.number().nullable().optional(),
  imageCropId: z.number().nullable().optional(),
  imageCropIdTo: z.number().nullable().optional(),
  x: z.number().nullable().optional(),
  xTo: z.any().nullable().optional(),
  depth: z.number().nullable().optional(),
  depthTo: z.any().nullable().optional(),
  alphaAngle: z
    .number()
    .nullable()
    .refine((val) => val === null || (val >= 0 && val <= 360), {
      message: "Alpha Angle must be between 0 and 360 degrees",
    })
    .optional(),
  betaAngle: z
    .number()
    .nullable()
    .refine((val) => val === null || (val >= 0 && val <= 90), {
      message: "Beta Angle must be between 0 and 90 degrees",
    })
    .optional(),
  width: z
    .number()
    .nullable()
    .refine((val) => val === null || !isNaN(val), {
      message: "Width must be a valid decimal number",
    })
    .optional(),
  structureConditionId: z.number().nullable().optional(),
  rockTypeId: z.number().nullable().optional(),
  icon: z.string().nullable().optional(),
  rockType: z
    .object({
      id: z.number().nullable().optional(),
      name: z.string().nullable().optional(),
    })
    .nullable()
    .optional(),
});

export type GeotechDataBodyType = z.TypeOf<typeof GeotechDataBody>;
