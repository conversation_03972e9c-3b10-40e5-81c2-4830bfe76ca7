import React, { memo, useCallback } from "react";
import { Tooltip, Tag, Spin } from "antd";
import { DeleteOutlined, CopyOutlined } from "@ant-design/icons";

// Enums
enum RowStatus {
  NEW = "NEW",
  EDITED = "EDITED",
  SAVED = "SAVED",
}

interface ActionCellProps {
  rowIndex: number;
  onDelete: (index: number) => void;
  onCopy: (index: number) => void;
  rowStatus: RowStatus;
  loading?: boolean;
}

// Optimized action cell component with better memoization
export const ActionCell = memo<ActionCellProps>(
  ({ rowIndex, onDelete, onCopy, rowStatus, loading }) => {
    const handleDelete = useCallback(
      () => onDelete(rowIndex),
      [onDelete, rowIndex]
    );
    const handleCopy = useCallback(() => onCopy(rowIndex), [onCopy, rowIndex]);

    return (
      <div className="flex gap-2 justify-center relative">
        <Tooltip title="Delete entry">
          <DeleteOutlined
            style={{ fontSize: 16 }}
            className="hover:text-primary cursor-pointer"
            onClick={handleDelete}
          />
        </Tooltip>
        <Tooltip title="Copy row">
          <CopyOutlined
            style={{ fontSize: 16 }}
            className="hover:text-primary cursor-pointer"
            onClick={handleCopy}
          />
        </Tooltip>
        <div className="flex items-center absolute bottom-0.5 left-2 !h-2">
          {rowStatus === RowStatus.NEW && (
            <div className="text-green-600 text-[14px]">
              {loading ? <Spin /> : "New"}
            </div>
          )}
          {rowStatus === RowStatus.EDITED && (
            <div className="text-blue-600 text-[14px]">
              {loading ? <Spin /> : "Edited"}
            </div>
          )}
        </div>
      </div>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison for better performance
    return (
      prevProps.rowIndex === nextProps.rowIndex &&
      prevProps.rowStatus === nextProps.rowStatus &&
      prevProps.loading === nextProps.loading &&
      prevProps.onDelete === nextProps.onDelete &&
      prevProps.onCopy === nextProps.onCopy
    );
  }
);

ActionCell.displayName = "ActionCell";

// Export the RowStatus enum for use in other components
export { RowStatus };
