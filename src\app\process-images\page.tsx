import { ProcessImageView } from "@/modules/process-images/components/process-image";
import { PERMISSIONS } from "@/constants/general.const";
import PermissionProvider from "@/components/layout/permission-provider";

export default function Page() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <ProcessImageView />
    </PermissionProvider>
  );
}
