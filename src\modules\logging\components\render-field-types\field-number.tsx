import React, { memo, useCallback } from "react";
import { InputNumber } from "antd";
import { Control, Controller } from "react-hook-form";
import { ErrorTooltip } from "./error-tooltip";

interface FieldNumberProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  unit?: string;
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldNumber = memo<FieldNumberProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    unit,
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation and shortcuts
        onKeyDown?.(event);
      },
      [onKeyDown]
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <div className="flex items-center gap-1">
              <InputNumber
                {...field}
                id={id}
                disabled={disabled}
                placeholder="Number"
                className={`flex-1 min-w-[100px] ${className || ""}`}
                onKeyDown={handleKeyDown}
                onFocus={handleFocus}
                onBlur={handleBlur}
                onChange={(value) => {
                  field.onChange(value);

                  // Trigger row status update
                  if (
                    onFieldChange &&
                    typeof rowIndex === "number" &&
                    fieldPath
                  ) {
                    onFieldChange(rowIndex, fieldPath, value);
                  }
                }}
              />
              {unit && (
                <span className="text-sm text-gray-600 whitespace-nowrap">
                  {unit}
                </span>
              )}
            </div>
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  }
);

FieldNumber.displayName = "FieldNumber";
