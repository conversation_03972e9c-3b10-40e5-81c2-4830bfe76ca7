import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { updateFontSize } from "@/modules/auth/redux/userSlice";
import { useQueryDrillhole } from "@/modules/drillhole/hooks/useQueryDrillhole";
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { useGetListGeologySuite } from "@/modules/geology-suite/hooks/useGetListGeologySuite";
import { useQueryGeologySuite } from "@/modules/geology-suite/hooks/useQueryGeologySuite";
import { useGetListGeotechSuite } from "@/modules/geotech-suite/hooks/useGetListGeotechSuite";
import { useQueryGeotechSuite } from "@/modules/geotech-suite/hooks/useQueryGeotechSuite";
import { getColoursList } from "@/modules/list/redux/thunks";
import { CopyOutlined, ExportOutlined, PlusOutlined } from "@ant-design/icons";
import { IconSearch } from "@tabler/icons-react";
import { Button, Select, Tooltip } from "antd";
import { useRouter, useSearchParams } from "next/navigation";
import { Fragment, useEffect, useRef, useState } from "react";
import { AiOutlineFontSize } from "react-icons/ai";
import { ImFontSize } from "react-icons/im";
import { toast } from "react-toastify";
import { useCreateDataEntry } from "../hooks/useCreateDataEntry";
import {
  updateAllLoggingsTable,
  updateLoggingSuiteSelected,
  updateRefetchLoggingView,
} from "../redux/loggingSlice";

export interface ILoggingDescProps {
  filter: any;
  geologySuites: any;
  modalState: any;
  setModalState: any;
  handleGeologySuiteChange: (suiteId: string) => void;
  handleGeoTechChange: (suiteId: string) => void;
  setSelectedGeophysics: (geophysics: string[]) => void;
  refetchDataEntry: () => void;
  geotechSuiteDetail: any;
  handleSelectGeotechSuite: (value: string) => void;
  setModalExport: (value: boolean) => void;
  allLoggingInfos: any;
}

export const convertColumnNameToDisplay = (value: string) => {
  if (!value) return "";
  const formatted = value.replace(/([A-Z])/g, " $1").trim();
  return formatted.charAt(0).toUpperCase() + formatted.slice(1);
};

export interface ILoggingEntry {
  id: string;
  depthFrom: number;
  depthTo: number;
  geologySuiteId: string;
  drillholeId: string;
  dataEntryValues: Array<{
    fieldType: FieldType;
    [key: string]: any;
  }>;
  [key: string]: any;
}

// Tách hàm xử lý dữ liệu logging

function LoggingEntriesGrid({
  handleGeologySuiteChange,
  handleGeoTechChange,
  refetchDataEntry,
  geotechSuiteDetail,
  handleSelectGeotechSuite,
  setModalExport,
}: ILoggingDescProps) {
  const allLoggingsTable =
    useAppSelector((state) => state.logging.allLoggingsTable) ?? [];
  const geologySuiteDetail = useAppSelector(
    (state) => state.geologySuite.detail
  );
  const allLoggings =
    useAppSelector((state) => state.logging.allLoggings) ?? [];
  const loggingSuiteMode = useAppSelector(
    (state) => state.logging.loggingSuiteMode
  );

  const [searchText, setSearchText] = useState<string>("");
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(getColoursList({ page: 1, pageSize: 1000 }));
  }, []);

  const containerRef = useRef<HTMLDivElement>(null);

  const fontSize = useAppSelector((state) => state.user.fontSize);
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const { request: requestGetListGeologySuite, data: geologySuites } =
    useGetListGeologySuite();
  useEffect(() => {
    requestGetListGeologySuite({
      maxResultCount: 100,
      projectId: globalProjectId,
    });
  }, [globalProjectId]);

  const { data: geotechSuites, request: requestGetListGeotechSuite } =
    useGetListGeotechSuite();

  useEffect(() => {
    requestGetListGeotechSuite({
      skipCount: 0,
      maxResultCount: 100,
    });
  }, []);

  const selectedDrillhole = useAppSelector(
    (state) => state.logging.selectedDrillHole
  );
  const { request: requestCreateDataEntry, loading: loadingCreateDataEntry } =
    useCreateDataEntry();
  const lastLogging = allLoggings[allLoggings.length - 1];

  const copyRow = () => {
    requestCreateDataEntry(
      {
        ...lastLogging,
        id: undefined,
        depthFrom: lastLogging?.depthTo ?? null,
        depthTo: null,
      },
      (res) => {
        toast.success("Copy row successfully");
        refetchDataEntry();
      },
      (error) => {
        toast.error(error?.message);
      }
    );
  };

  const addRow = async () => {
    const dataEntry = geologySuiteDetail?.geologySuiteFields.map((item) => {
      return {
        geologysuiteFieldId: item?.id,
        fieldType: item.geologyField?.type,
      };
    });

    const payload = {
      depthFrom: lastLogging?.depthTo ?? 0,
      depthTo: null,
      geologySuiteId: geologySuiteDetail?.id,
      dataEntryValues: dataEntry,
      drillholeId: selectedDrillhole?.value as any,
    };

    requestCreateDataEntry(
      payload,
      (res) => {
        toast.success("Add row successfully");
        refetchDataEntry();
        dispatch(updateRefetchLoggingView());
      },
      (error) => {
        toast.error(error?.message);
      }
    );
  };

  const loggingSuiteSelected = useAppSelector(
    (state) => state.logging.loggingSuiteSelected
  );
  const geotechSelected = useAppSelector(
    (state) => state.logging.geotechSelected
  );

  const isShowAddAndCopyRow =
    loggingSuiteMode === "Geology" && loggingSuiteSelected;

  const searchParams = useSearchParams();
  const queries: any = {};
  for (const [key, value] of searchParams.entries()) {
    const arrayValues = searchParams.getAll(key);
    queries[key] = arrayValues.length > 1 ? arrayValues : value;
  }
  const router = useRouter();
  const params = new URLSearchParams(queries);

  const geotechSuiteIdParams = params.get("geotechSuiteId");
  const loggingSuiteIdParams = params.get("loggingSuiteId");

  console.log(loggingSuiteIdParams);
  console.log("dm");

  const {
    data: _geologySuites,
    setSearchParams: setSearchParamsGeologySuite,
    setEnable: setEnableGeologySuite,
  } = useQueryGeologySuite();
  useEffect(() => {
    setEnableGeologySuite(true);
  }, []);
  const {
    data: drillholes,
    setSearchParams: setSearchParamsDrillhole,
    searchParams: searchParamsDrillhole,
  } = useQueryDrillhole();
  const handleDrillholeScroll = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setSearchParamsDrillhole({
        ...searchParamsDrillhole,
        maxResultCount: (searchParamsDrillhole?.maxResultCount ?? 0) + 10,
      });
    }
  };
  const { data: _geotechSuites, setEnable: setEnableGeotechSuite } =
    useQueryGeotechSuite();
  useEffect(() => {
    setEnableGeotechSuite(true);
  }, []);
  const handleOnChangeLoggingSuite = (value: string) => {
    dispatch(updateLoggingSuiteSelected(value));
    const suiteId = value?.split("-")[1];
    if (value?.startsWith("geologySuites")) {
      handleGeologySuiteChange(suiteId);
    } else {
      handleGeoTechChange(suiteId);
    }
  };
  console.log(loggingSuiteIdParams);

  return (
    <div className="w-full h-full">
      <Fragment>
        <div
          className="flex gap-2 lg:flex-row flex-col my-3"
          ref={containerRef}
        >
          <div className="flex flex-col md:flex-row gap-2">
            <div className="px-5 rounded-lg flex items-center gap-2 h-[38px] w-[200px] bg-white border">
              <IconSearch />
              <input
                type="text"
                placeholder="Search"
                value={searchText}
                className="w-full font-normal outline-none text-primary placeholder:text-gray80"
                onChange={(e) => {
                  setSearchText(e.target.value);
                }}
              />
            </div>
            <div className="flex gap-2  items-center">
              <p className="font-bold">Font Size</p>
              <Tooltip title="Decrease font size">
                <Button
                  type="primary"
                  icon={<AiOutlineFontSize />}
                  onClick={() => dispatch(updateFontSize(fontSize - 1))}
                />
              </Tooltip>
              <Tooltip title="Increase font size">
                <Button
                  type="primary"
                  icon={<ImFontSize />}
                  onClick={() => dispatch(updateFontSize(fontSize + 1))}
                />
              </Tooltip>
            </div>
          </div>
          <div className="flex md:flex-row flex-col gap-2 md:items-end ">
            <div className="flex gap-2 lg:items-center flex-col lg:flex-row">
              <p className="font-bold">Logging Suite</p>
              <Select
                allowClear
                onClear={() => {
                  dispatch(updateLoggingSuiteSelected(""));
                  dispatch(updateAllLoggingsTable([]));
                }}
                value={loggingSuiteIdParams}
                key={loggingSuiteIdParams}
                className="w-[200px]"
                placeholder="Choose suite"
                options={(_geologySuites?.data?.items ?? [])
                  .map((attribute) => ({
                    label: attribute.name,
                    value: `geologySuites-${attribute.id}`,
                  }))
                  .concat(
                    (_geotechSuites?.data?.items ?? []).map((geotechSuite) => ({
                      label: geotechSuite.name,
                      value: `geotechSuites-${geotechSuite.id}`,
                    }))
                  )}
                onChange={handleOnChangeLoggingSuite}
              />
            </div>
            {loggingSuiteIdParams?.startsWith("geologySuites") && (
              <div className="flex gap-2">
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={addRow}
                  loading={loadingCreateDataEntry}
                >
                  Add Row
                </Button>
                <Button
                  type="primary"
                  icon={<CopyOutlined />}
                  onClick={copyRow}
                  loading={loadingCreateDataEntry}
                  disabled={
                    allLoggingsTable.length === 0 ||
                    lastLogging?.depthTo === 0 ||
                    lastLogging?.depthTo === null
                  }
                >
                  Copy Row
                </Button>
              </div>
            )}

            <div className="">
              <button
                onClick={() => {
                  setModalExport?.(true);
                }}
                className="flex items-center gap-2 p-1 bg-[#0F763D] text-white rounded-md hover:bg-[#0F763D]/80"
              >
                <ExportOutlined />
                Export
              </button>
            </div>
          </div>

          {loggingSuiteMode === "Geotech" && (
            <div className="flex gap-2 lg:items-center">
              <p className="font-bold">Geotech</p>
              <Select
                placeholder="Choose suite"
                options={(geotechSuiteDetail?.structures ?? []).map(
                  (geotechSuite) => ({
                    label: geotechSuite.name,
                    value: geotechSuite.id,
                  })
                )}
                key={JSON.stringify(geotechSuiteIdParams)}
                value={geotechSuiteIdParams}
                allowClear
                filterOption={false}
                onChange={handleSelectGeotechSuite}
              />
            </div>
          )}
        </div>
      </Fragment>
    </div>
  );
}

export default LoggingEntriesGrid;
