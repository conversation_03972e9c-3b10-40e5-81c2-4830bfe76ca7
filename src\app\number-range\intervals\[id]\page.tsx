"use client";

import { RequestState } from "@/common/configs/app.contants";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import TableNumberRangeIntervals from "@/modules/number-range/components/table-number-range-intervals";
import { Spin } from "antd";
import { useParams } from "next/navigation";

export default function NumberRangeIntervalsPage() {
  const params = useParams();
  const numberRange = useAppSelector((state) => state.numberRange);
  const isLoading = numberRange.status === RequestState.pending;

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-[calc(100vh-64px)]">
        <Spin size="large" />
      </div>
    );
  }

  return <TableNumberRangeIntervals numberRangeId={Number(params.id)} />;
}
