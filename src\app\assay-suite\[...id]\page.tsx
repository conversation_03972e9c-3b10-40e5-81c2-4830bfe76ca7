import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableAssaySuiteAttribute from "@/modules/assay-suite-field/components/table-assay-suite-attribute";

export default function Page({ params }: { params: { id: string } }) {
  const id = params.id[0];
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <TableAssaySuiteAttribute id={id} />
    </PermissionProvider>
  );
}
