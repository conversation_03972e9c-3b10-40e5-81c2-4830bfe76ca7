import React, { memo } from "react";
import { TableGeologyHeaderProps } from "./table-geology-types";

export const TableGeologyHeader = memo<TableGeologyHeaderProps>(
  ({ columnIndex, column, style, fontSize, isMobile, key }) => {
    // Determine if this is a fixed column for styling
    const isFixedColumn = !isMobile && columnIndex < 3;
    const isLastFixedColumn = !isMobile && columnIndex === 2;

    return (
      <div
        key={key}
        style={{
          ...style,
          display: "flex",
          alignItems: "center",
          padding: "8px 12px",
          borderRight: "1px solid #e5e7eb",
          borderBottom: "2px solid #d1d5db",
          fontSize: `${fontSize}px`,
          fontWeight: 600,
          backgroundColor: "#f9fafb", // Blue background for first 3 columns for debugging
          color: "#374151",
          overflow: "hidden",
          boxSizing: "border-box",
          ...(isLastFixedColumn && {
            borderRight: "2px solid #d1d5db", // Stronger border for last fixed column
          }),
        }}
        className={`header-cell ${
          isLastFixedColumn ? "last-fixed-column" : ""
        }`}
      >
        <div
          className="truncate w-full"
          style={{
            whiteSpace: "nowrap",
            overflow: "hidden",
            textOverflow: "ellipsis",
            minHeight: "20px", // Ensure minimum height for text
            lineHeight: "20px", // Ensure proper line height
          }}
          title={column.title} // Tooltip for truncated text
        >
          {column.title}
        </div>
      </div>
    );
  }
);

TableGeologyHeader.displayName = "TableGeologyHeader";
