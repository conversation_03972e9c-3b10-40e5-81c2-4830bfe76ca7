import { Group, Text } from "react-konva";
import { DrillHoleViewStack } from "../../model/entities/drillhole.config";
import ZoomableImage from "./zoomable-image";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import {
  selectDHViewConfig,
  selectDHViewInfo,
  selectImages,
  updateDHViewInfo,
} from "../../redux/imageSlice";
import { memo } from "react";
import TableKonva from "./table.konva";
import { get, isNumber } from "lodash";
import { ImageSizeEnum } from "../../model/enum/images.enum";
import { EnumDrillholeView } from "../../model/enum/drillhole.enum";
import { getAvailableImage } from "../../helpers/image.helpers";

const ImageColumn = ({ images }: { images: DrillHoleViewStack }) => {
  const dHViewConfigs = useAppSelector(selectDHViewConfig);
  const dHViewInfo = useAppSelector(selectDHViewInfo);
  const previewImageSize = useAppSelector(selectImages)?.previewSize;
  const drillHoleViewMode = useAppSelector(selectImages).drillholeViewMode;

  const dispatch = useAppDispatch();

  const handlePreview = (image) => {
    if (drillHoleViewMode === EnumDrillholeView.Original) {
      const url = getAvailableImage(image.files, previewImageSize)?.url;
      dispatch(
        updateDHViewInfo({
          urlPreview: url,
        })
      );
      return;
    }
    if (previewImageSize === ImageSizeEnum.FULL_SIZE) {
      dispatch(
        updateDHViewInfo({
          urlPreview: image.urlCroppedImage,
        })
      );
      return;
    }
    const availableMediumSize = image?.mediumSize ?? image.urlCroppedImage;
    dispatch(
      updateDHViewInfo({
        urlPreview: availableMediumSize,
      })
    );
  };

  const getCategoryName = (category) => {
    switch (category) {
      case 1:
        return "Standard";
      case 2:
        return "Hyperspectral";
      case 3:
        return "Optical";
      case 4:
        return "Geology data";
      case 5:
        return "Rig";
      default:
        return "--";
    }
  };

  const tableData = [
    [images?.drillhole?.name],
    images?.typeName ? [images?.typeName] : undefined,
    images?.subTypeName ? [images?.subTypeName] : undefined,
  ].filter(Boolean);

  return (images?.data ?? []).map((image) => {
    return (
      <Group key={image.id}>
        {!dHViewInfo?.isHideTableInfo && (
          <TableKonva
            startX={images.startX - dHViewConfigs.displacementX}
            startY={
              isNumber(images.minHeight)
                ? images.minHeight -
                  (tableData.length + 1) * dHViewConfigs.cellHeight
                : 0
            }
            data={tableData as any}
            cellHeight={dHViewConfigs.cellHeight}
            cellWidth={dHViewConfigs.cellWidth}
            fontSize={dHViewConfigs.fontSize}
            strokeWidth={dHViewConfigs.strokeWidth}
          />
        )}

        {dHViewInfo?.isShowTextDepth && (
          <Text
            x={
              images.startX -
              (String(image.depthFrom).length + 2) *
                (dHViewConfigs.depthFromSize / 2) -
              0.08
            }
            y={image.depthFrom - dHViewConfigs.depthFromSize / 2}
            text={`${image.depthFrom} -`}
            fontSize={dHViewConfigs.depthFromSize}
            fill="black"
          ></Text>
        )}

        <ZoomableImage
          key={image.displayUrl}
          src={image.displayUrl}
          x={images.startX}
          y={image.depthFrom}
          width={image.width}
          height={image.height}
          onClick={() => handlePreview(image)}
          {...images.uiProps}
        />

        {dHViewInfo?.isShowTextDepth && (
          <Text
            x={
              images.startX -
              (String(image.depthTo).length + 2) *
                (dHViewConfigs.depthToSize / 2) -
              0.08
            }
            y={image.depthTo - dHViewConfigs.depthToSize / 2}
            text={`${image.depthTo} -`}
            fontSize={dHViewConfigs.depthToSize}
            fill="black"
          ></Text>
        )}
      </Group>
    );
  });
};

export default memo(ImageColumn);
