import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import TableDrillHole from "@/modules/drillhole/components/table-drillhole";

export default function Page() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <TableDrillHole />
    </PermissionProvider>
  );
}
