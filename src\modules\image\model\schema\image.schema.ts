import { z } from "zod";

export const depthFilterSchema = z
  .object({
    depthFrom: z.number().nullable(),
    depthTo: z.number().nullable(),
  })
  .strict()
  .superRefine(({ depthFrom, depthTo }, ctx) => {
    if (depthFrom === null && depthTo === null) {
      ctx.addIssue({
        code: "custom",
        message: "Depth from cannot be empty",
        path: ["depthFrom"],
      });
      ctx.addIssue({
        code: "custom",
        message: "Depth to cannot be empty",
        path: ["depthTo"],
      });
    }
    if (depthFrom !== null && depthTo !== null) {
      if (depthFrom > depthTo) {
        ctx.addIssue({
          code: "custom",
          message: "Depth from cannot be greater than Depth to",
          path: ["depthFrom"],
        });
        ctx.addIssue({
          code: "custom",
          message: "Depth to cannot be less than Depth from",
          path: ["depthTo"],
        });
      } else if (depthFrom === depthTo) {
        ctx.addIssue({
          code: "custom",
          message: "Depth from and Depth to cannot be equal",
          path: ["depthFrom"],
        });
        ctx.addIssue({
          code: "custom",
          message: "Depth from and Depth to cannot be equal",
          path: ["depthTo"],
        });
      }
    }
  });

export const ImageBody = z.object({
  id: z.number().optional(),
  boundingBox: z.string().optional(),
  boundingRows: z.string().optional(),
  depthFrom: z.number().optional(),
  depthTo: z.number().optional(),
  status: z.number().optional(),
  projectId: z.number().optional(),
  prospectId: z.number().optional(),
  drillHoleId: z.number().optional(),
  type: z.number().optional(),
  imageTypeId: z.number().optional(),
  imageSubTypeId: z.number().optional(),
});

export type ImageBodyType = z.TypeOf<typeof ImageBody>;

export type DepthFilterSchemaType = z.TypeOf<typeof depthFilterSchema>;
