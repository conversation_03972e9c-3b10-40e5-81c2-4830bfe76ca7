import { Circle, Group, Line } from "react-konva";
import { SegmentationResult } from "../model/dtos/logging.config";

type Props = {
  segmentations: SegmentationResult;
  isEnableFill: boolean;
};

const SegmentationPoint = ({ segmentations, isEnableFill = true }: Props) => {
  //this is a list of points that will be used to draw the segmentations
  const { isPolyComplete, points } = segmentations;
  return (
    <Line
      points={points?.flat()}
      stroke="yellow"
      strokeWidth={5}
      closed={isPolyComplete}
      // fill={isEnableFill ? segmentations.color : undefined}
    />
  );
};

export default SegmentationPoint;
