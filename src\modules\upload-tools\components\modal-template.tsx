import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { ModalCommon } from "@/components/common/modal-common";
import { useCreateMappingTemplate } from "@/modules/mapping-template/hooks/useCreateMappingTemplate";
import { useUpdateMappingTemplate } from "@/modules/mapping-template/hooks/useUpdateMappingTemplate";
import { ArrowRightOutlined, SaveOutlined } from "@ant-design/icons";
import { Button, Input, Select, Switch, Tag } from "antd";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { ImportFileType } from "../api/importData.api";
import { setTemplateSelected } from "../redux/importDataSlice/import-data.slice";

export interface IModalTemplateProps {
  open: boolean;
  onCancel: () => void;
  refetchTemplate: () => void;
  setValue: any;
}

export const ModalTemplate = ({
  open,
  onCancel,
  refetchTemplate,
  setValue,
}: IModalTemplateProps) => {
  const [createNewField, setCreateNewField] = useState(false);
  const templateSelected = useAppSelector(
    (state) => state.importData.templateSelected
  );
  const geologySuite = useAppSelector((state) => state.importData.geologySuite);
  const [template, setTemplate] = useState<any>(templateSelected);

  const { header } = useAppSelector((state) => state.importData);
  const { request: requestUpdateTemplate, loading: loadingUpdateTemplate } =
    useUpdateMappingTemplate();
  const { request: requestCreateTemplate, loading: loadingCreateTemplate } =
    useCreateMappingTemplate();
  const dispatch = useAppDispatch();
  const handleSaveTemplate = async () => {
    if (!template.name) {
      toast.error("Template name is required");
      return;
    }
    let payload = {
      id: template.id,
      name: template.name,
      importFileType: template.importFileType,
      fields: template.importMappingTemplateFields,
      suiteId: template.suiteId,
    };
    let geologySuiteFields = geologySuite.geologySuiteFields?.map((d: any) => {
      return d.name;
    });
    const isGeologyType = template.importMappingTemplateFields.some((d) => {
      return d?.systemFieldName === "Depth From";
    });
    let result: string[] = [];
    if (isGeologyType) {
      result = ["DrillHole", "Depth From", "Depth To"];
      result = [...result, ...geologySuiteFields];
    }
    const newpayload = payload.fields
      .map((d) => {
        const findIndex = result.findIndex(
          (name) => name === d?.systemFieldName
        );
        return {
          ...d,
          sequence: findIndex,
        };
      })
      .sort((a, b) => a.sequence - b.sequence);
    payload.fields = newpayload;

    if (createNewField) {
      payload.id = undefined;
      requestCreateTemplate(
        payload,
        (res) => {
          dispatch(setTemplateSelected(res));
          setValue("ImportMappingTemplateId", res.id);
          toast.success("Create template success");
          onCancel();
          refetchTemplate();
        },
        () => {
          toast.error("Create template failed");
        }
      );
    } else {
      requestUpdateTemplate(
        payload,
        (res) => {
          dispatch(setTemplateSelected(res));
          setValue("ImportMappingTemplateId", res.id);
          toast.success("Update template success");
          onCancel();
          refetchTemplate();
        },
        () => {
          toast.error("Update template failed");
        }
      );
    }
  };
  useEffect(() => {
    if (createNewField) {
      setTemplate({
        ...template,
        name: "",
      });
    } else {
      setTemplate({
        ...template,
        name: templateSelected.name,
      });
    }
  }, [createNewField]);

  return (
    <ModalCommon
      open={open}
      onCancel={onCancel}
      footer={null}
      width={800}
      closable={false}
      centered
    >
      <div className="flex flex-col gap-2  overflow-y-auto">
        <p className="font-bold flex flex-col gap-2">
          Name: {""}
          <Input
            value={template?.name}
            onChange={(e) => {
              setTemplate({ ...template, name: e.target.value });
            }}
            size="large"
            placeholder="Template Name"
          />
        </p>
        <p className="font-bold flex gap-2">
          Import File Type:{" "}
          {template?.importFileType === ImportFileType.Geology
            ? "Geology"
            : templateSelected?.importFileType === ImportFileType.Geophysics
            ? "Geophysics"
            : templateSelected?.importFileType === ImportFileType.Assay
            ? "Assay"
            : "Downhole Survey"}
        </p>
        <p className="font-bold flex gap-2">Suite: {template?.suiteName}</p>
        <div className="max-h-[400px] overflow-y-auto flex flex-col gap-2">
          {template?.importMappingTemplateFields?.map((d: any) => (
            <div className="grid grid-cols-7 gap-3 items-center">
              <Tag color="red" className="text-xl font-bold col-span-3">
                {d.systemFieldName}
              </Tag>
              <ArrowRightOutlined className="col-span-1" />
              <Select
                className="col-span-3"
                options={header.map((h: any) => ({
                  label: h,
                  value: h,
                }))}
                size="large"
                value={d.fileColumnName}
                onChange={(value) => {
                  setTemplate({
                    ...template,
                    importMappingTemplateFields:
                      template.importMappingTemplateFields.map((f: any) =>
                        f.systemFieldName === d.systemFieldName
                          ? { ...f, fileColumnName: value }
                          : f
                      ),
                  });
                }}
              />
            </div>
          ))}
        </div>
        <div className="flex gap-2">
          <p className="font-bold">Create new mapping template field</p>
          <Switch
            checked={createNewField}
            onChange={(checked) => setCreateNewField(checked)}
          />
        </div>
        {/* {createNewField && (
          <div className="flex gap-2">
            <Input
              value={newField}
              onChange={(e) => setNewField(e.target.value)}
              size="large"
            />
            <Button
              type="primary"
              size="large"
              icon={<PlusOutlined />}
              onClick={handleCreateNewField}
            />
          </div>
        )} */}

        <Button
          type="primary"
          size="large"
          icon={<SaveOutlined />}
          onClick={handleSaveTemplate}
          loading={loadingUpdateTemplate || loadingCreateTemplate}
        >
          {createNewField ? "Create" : "Save"}
        </Button>
      </div>
    </ModalCommon>
  );
};
