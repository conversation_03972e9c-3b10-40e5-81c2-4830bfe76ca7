import { useDebounce } from "@/hooks/useDebounce";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import drillholeRequest from "../api/drillhole.api";
import { DrillholeQuery } from "../interface/drillhole.query";

interface ScrollEvent {
  target: {
    scrollTop: number;
    offsetHeight: number;
    scrollHeight: number;
  };
}

export const useQueryDrillhole = () => {
  const [enable, setEnable] = useState(false);
  const [isEnd, setIsEnd] = useState(false);
  const isFirstRender = useRef(true);

  const [searchParams, setSearchParams] = useState<DrillholeQuery>({
    isActive: true,
    maxResultCount: 100,
    skipCount: 0,
  });

  // Memoize search params to prevent unnecessary re-renders
  const memoizedSearchParams = useMemo(
    () => searchParams,
    [
      searchParams.isActive,
      searchParams.maxResultCount,
      searchParams.skipCount,
      searchParams.keyword,
      searchParams.projectIds,
      searchParams.prospectIds,
    ]
  );

  // Throttled scroll handler with proper pagination
  const handleScroll = useCallback(
    (event: ScrollEvent) => {
      const target = event.target;
      const isAtBottom =
        target.scrollTop + target.offsetHeight >= target.scrollHeight - 1;

      if (isAtBottom && !isEnd) {
        setSearchParams((prev) => ({
          ...prev,
          maxResultCount: (prev.maxResultCount ?? 0) + 10,
        }));
      }
    },
    [isEnd]
  );
  // Reset pagination when keyword, projectIds, or prospectIds changes
  useEffect(() => {
    // Skip the first render to prevent unnecessary API calls
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // Reset pagination when any search parameter changes
    setIsEnd(false);
    setSearchParams((prev) => ({
      ...prev,
      skipCount: 0,
      maxResultCount: 100,
    }));
  }, [searchParams.keyword, searchParams.projectIds, searchParams.prospectIds]);

  const debouncedSearch = useDebounce(searchParams?.keyword, 500);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [
      "drillholes",
      { ...memoizedSearchParams, keyword: debouncedSearch },
    ],
    queryFn: () => {
      return drillholeRequest.getListDrillHole({
        ...memoizedSearchParams,
        keyword: debouncedSearch,
      });
    },
    enabled: enable && !isEnd,
    placeholderData: keepPreviousData,
  });

  // Debug logging
  useEffect(() => {}, [enable, isEnd, memoizedSearchParams]);

  // Improved end detection logic
  useEffect(() => {
    if (data?.data?.pagination) {
      const { total } = data.data.pagination;
      const currentCount = searchParams.maxResultCount ?? 0;

      if (currentCount >= total) {
        setIsEnd(true);
      }
    }
  }, [data, searchParams.maxResultCount]);

  return {
    data,
    isLoading,
    error,
    refetch,
    setSearchParams,
    searchParams: memoizedSearchParams,
    setEnable,
    handleScroll,
    isEnd,
    enable,
  };
};
