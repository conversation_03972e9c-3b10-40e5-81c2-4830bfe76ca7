"use client";
import type { ColorPickerProps } from "antd";
import { App, ColorPicker, Input } from "antd";
import { useState } from "react";
import { UseFormGetValues, UseFormSetValue } from "react-hook-form";
import { FormItem } from "react-hook-form-antd";

const ColorPickerCommon = ({
  control,
  name,
  setValue,
  getValues,
  label,
  placeholder,
  isRequired,
}: {
  control: any;
  getValues: UseFormGetValues<any>;
  setValue: UseFormSetValue<any>;
  name: string;
  label: string;
  placeholder?: string;
  isRequired?: boolean;
}) => {
  const [valueColor, setValueColor] = useState<ColorPickerProps["value"]>(
    getValues(name as any) || "#FFFFFF"
  );
  return (
    <div className="flex flex-col gap-2">
      <p className="font-medium">
        {label}{" "}
        {isRequired ? (
          <span className="text-14-16 md:text-16-20 text-error">*</span>
        ) : null}
      </p>
      <div className="flex gap-2">
        <FormItem className="flex-1" control={control} name={name}>
          <Input disabled name={name} placeholder={placeholder} />
        </FormItem>
        <ColorPicker
          value={valueColor}
          onChangeComplete={(color) => {
            setValueColor(color);
            setValue(name as any, color.toHexString());
          }}
        />
      </div>
    </div>
  );
};

export default ColorPickerCommon;
