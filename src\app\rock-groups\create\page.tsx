import PermissionProvider from "@/components/layout/permission-provider";
import { PERMISSIONS } from "@/constants/general.const";
import RockGroupDetail from "@/modules/rock-groups/components/rock-group-detail";
export default function Page({ params }: { params: { id: string } }) {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <RockGroupDetail />
    </PermissionProvider>
  );
}
