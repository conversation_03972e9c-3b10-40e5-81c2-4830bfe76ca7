import { PERMISSIONS } from "@/constants/general.const";
import PermissionProvider from "@/components/layout/permission-provider";
import ImageListView from "@/modules/image/components/image-list-view";
import { convertSearchParamsToString } from "@/utils/string.utils";

export default function Page({ searchParams }) {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
      redirectBack={encodeURIComponent(
        `/images?${convertSearchParamsToString(searchParams)}`
      )}
    >
      <ImageListView />
    </PermissionProvider>
  );
}
