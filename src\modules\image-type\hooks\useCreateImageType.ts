import { useState } from "react";
import imageTypeRequest from "../api/image-type.api";
import { ImageTypeBodyType } from "../model/schema/image-type.schema";

export const useCreateImageType = () => {
  const [loading, setLoading] = useState(false);
  async function request(
    params: ImageTypeBodyType,
    onSuccess?: Function,
    onError?: Function
  ) {
    setLoading(true);
    const response = await imageTypeRequest.create(params);
    if (response.state === "success") {
      onSuccess && onSuccess(response.data);
      setLoading(false);
    } else {
      onError && onError(response);
      setLoading(false);
    }
  }
  return {
    request,
    loading,
  };
};
