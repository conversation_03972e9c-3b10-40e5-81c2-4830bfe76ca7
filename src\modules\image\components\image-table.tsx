import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { ButtonCommon } from "@/components/common/button-common";
import { ModalCommon } from "@/components/common/modal-common";
import { TableCommon } from "@/components/common/table-common";
import { AimOutlined, EditOutlined, EyeOutlined } from "@ant-design/icons";
import { Image, TableColumnsType, Tag, Tooltip } from "antd";
import dayjs from "dayjs";
import { useRouter } from "next/navigation";
import { useState } from "react";
import {
  ImageCategory,
  ImageSizeEnum,
  ImageStatus,
  StandardType,
  Type,
} from "../model/enum/images.enum";
import DrillholeViewPanel from "./drillhole-view-panel";
import { ModalEditImage } from "./modal-edit-image";

export default function ImageTable({
  selectedRowKeys,
  setSelectedRowKeys,
}: {
  selectedRowKeys: number[];
  setSelectedRowKeys: (keys: number[]) => void;
}) {
  const imagesOfDrillHoles = useAppSelector(
    (state) => state.images.imagesOfDrillHoles
  );
  const router = useRouter();

  const listImages = imagesOfDrillHoles
    .map((image) => {
      return image.list;
    })
    .flat();
  const columns: TableColumnsType<any> = [
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      render: (_, record, index) => {
        return (
          <div className="flex gap-3">
            <EditOutlined
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
              onClick={() => {
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "edit",
                  detailInfo: record,
                });
              }}
            />
            <EyeOutlined
              onClick={() => {
                router.push(`/images/${record.id}`);
              }}
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
            <AimOutlined
              onClick={() =>
                setModalState({
                  ...modalState,
                  isOpen: true,
                  type: "ocr",
                  detailInfo: record,
                })
              }
              style={{ fontSize: 16 }}
              className="hover:text-primary cursor-pointer"
            />
          </div>
        );
      },
    },

    {
      title: "Image",
      dataIndex: "name",
      key: "name",
      render(value, record, index) {
        return (
          <div className="flex gap-3">
            <Image
              src={
                record?.files?.find((file: any) => file.size === "medium")
                  ?.url ??
                record?.files?.find(
                  (file: any) => file.size === ImageSizeEnum.FULL_SIZE
                )?.url
              }
              preview={{
                src:
                  record?.files?.find(
                    (file: any) => file.size === ImageSizeEnum.FULL_SIZE
                  )?.url || record?.files?.[0]?.url,
              }}
              alt=""
              className="w-20 h-20 object-cover rounded-lg"
              width={100}
              height={100}
            />
            <p>{value}</p>
          </div>
        );
      },
    },

    {
      title: "Drillhole",
      dataIndex: "drillhole",
      key: "drillhole",
      render(value, record, index) {
        return <p>{record?.drillHole?.name}</p>;
      },
    },
    {
      title: "Project",
      dataIndex: "project",
      key: "project",
      render(value, record, index) {
        return (
          <Tag
            style={{
              backgroundColor: record?.project?.backgroundColor,
              color: record?.project?.textColor,
            }}
          >
            {record?.project?.name}
          </Tag>
        );
      },
    },
    {
      title: "Prospect",
      dataIndex: "prospect",
      key: "prospect",
      render(value, record, index) {
        return (
          <Tag
            style={{
              backgroundColor: record?.prospect?.backgroundColor,
              color: record?.prospect?.textColor,
            }}
          >
            {value?.name}
          </Tag>
        );
      },
    },
    {
      title: "Depth From",
      dataIndex: "depthFrom",
      key: "depthFrom",
    },
    {
      title: "Depth To",
      dataIndex: "depthTo",
      key: "depthTo",
    },
    {
      title: "Image Category",
      dataIndex: "imageCategory",
      key: "imageCategory",
      render(value, record, index) {
        switch (value) {
          case ImageCategory.Drilling:
            return <Tag color="magenta">Drilling</Tag>;
          case ImageCategory.Map:
            return <Tag color="green">Map</Tag>;
          case ImageCategory.General:
            return <Tag color="blue">General</Tag>;
          default:
            return null;
        }
      },
    },

    {
      title: "Image Status",
      dataIndex: "imageStatus",
      key: "imageStatus",
      render(value, record, index) {
        switch (value) {
          case ImageStatus.NotStarted:
            return <Tag color="magenta">Not Started</Tag>;
          case ImageStatus.InProgress:
            return <Tag color="green">In Progress</Tag>;
          case ImageStatus.Reprocess:
            return <Tag color="blue">Reprocess</Tag>;
          case ImageStatus.Review:
            return <Tag color="cyan">Review</Tag>;
          case ImageStatus.Complete:
            return <Tag color="purple">Complete</Tag>;
          case ImageStatus.Unnamed:
            return <Tag color="red">Unnamed</Tag>;
          default:
            return null;
        }
      },
    },
    {
      title: "Image type",
      dataIndex: "imageType",
      key: "imageType",
      render(value, record, index) {
        return <p>{value?.name}</p>;
      },
    },
    {
      title: "Image Subtype",
      dataIndex: "imageSubtype",
      key: "imageSubtype",
      render(value, record, index) {
        return <p>{value?.name}</p>;
      },
    },
    {
      title: "Created At",
      dataIndex: "creationTime",
      key: "creationTime",
      render(value, record, index) {
        return (
          <p>{dayjs(record?.creationTime).format("DD/MM/YYYY HH:mm:ss")}</p>
        );
      },
    },
    {
      title: "Created By",
      dataIndex: "createdByName",
      key: "createdByName",
      render(value, record, index) {
        return (
          <Tooltip title={record?.createdByUser}>
            <p>{value}</p>
          </Tooltip>
        );
      },
    },
  ];
  const [modalState, setModalState] = useState<any>({
    isOpen: false,
    type: "create",
  });
  return (
    <div className="flex gap-4">
      {modalState.isOpen && (
        <ModalCommon
          open={modalState.isOpen}
          centered
          padding={0}
          footer={null}
          onCancel={() => {
            setModalState({
              ...modalState,
              isOpen: false,
            });
          }}
          width={600}
          style={{ borderRadius: 8 }}
          closable={false}
        >
          {modalState.type === "delete" && (
            <>
              <div className="flex flex-col gap-2">
                <p className="font-bold text-16-18 capitalize font-visby">
                  Are you sure you want to delete these images?
                </p>
                <p>
                  This action cannot be undone. This will permanently delete the
                  project
                </p>
                <div className="flex justify-end gap-2">
                  <ButtonCommon
                    onClick={() => {
                      setModalState({
                        ...modalState,
                        isOpen: false,
                      });
                    }}
                    className="btn btn-sm"
                  >
                    No
                  </ButtonCommon>
                  <ButtonCommon
                    // loading={loadingDeleteImages}
                    onClick={() => {}}
                    className="btn btn-sm bg-primary text-white hover:bg-primary"
                  >
                    Yes
                  </ButtonCommon>
                </div>
              </div>
            </>
          )}
          {modalState.type === "edit" && (
            <ModalEditImage
              modalState={modalState}
              setModalState={setModalState}
              // refresh={refresh}
            />
          )}
        </ModalCommon>
      )}
      <DrillholeViewPanel />
      <TableCommon
        className="font-visby flex-1"
        style={{
          width: "80%",
        }}
        rowSelection={{
          onChange: (selectedRowKeys: any, selectedRows: any) => {
            setSelectedRowKeys(selectedRowKeys);
          },
        }}
        rowKey={(record) => (record as any).id}
        columns={columns as any}
        dataSource={listImages}
      />
    </div>
  );
}
