"use client";
import { Select } from "antd";
import { FormItem } from "react-hook-form-antd";

export interface IInputTextCommonProps {
  label?: string;
  name: string;
  placeholder?: string;
  control?: any;
  size?: "large" | "middle" | "small";
  options?: any;
  disabled?: boolean;
  onSearch?: (value: string) => void;
  filterOption?: any;
  showSearch?: boolean;
  notFoundContent?: any;
  onChange?: (value: any, option: any) => void;
  optionFilterProp?: string;
  mode?: "multiple" | "tags";
  allowClear?: boolean;
  defaultActiveFirstOption?: boolean;
  isRequired?: boolean;
  loading?: boolean;
  searchValue?: string;
  defaultValue?: any;
  onPopupScroll?: (e: any) => void;
  onBlur?: () => void;
  onSelect?: (value: any, option: any) => void;
  onClear?: () => void;
  tagRender?: (props: any) => any;
  value?: any;
  watch?: any;
  setValue?: any;
  className?: string;
  note?: any;
  onDeselect?: (value: any) => void;
  horizontal?: boolean;
  labelRender?: ((props: any) => React.ReactNode) | undefined;
}

export function SelectCommon(props: IInputTextCommonProps) {
  const {
    label,
    name,
    placeholder,
    control,
    size = "middle",
    options,
    disabled,
    onSearch,
    filterOption,
    showSearch,
    notFoundContent,
    onChange,
    optionFilterProp,
    mode,
    allowClear,
    defaultActiveFirstOption = false,
    isRequired = false,
    loading = false,
    onPopupScroll,
    searchValue,
    onBlur,
    onSelect,
    onClear,
    tagRender,
    defaultValue,
    watch,
    className,
    note,
    horizontal,
    labelRender,
  } = props;

  return (
    <div
      className={`flex ${
        horizontal ? "flex-row items-center" : "flex-col"
      } gap-2 ${className}`}
    >
      <div className="flex gap-1 items-center">
        {label && <p className="font-medium">{label}</p>}
        {note && note}
        {isRequired ? (
          <span className="text-14-16 md:text-16-20 text-error">*</span>
        ) : null}
      </div>
      <FormItem control={control} name={name} className="flex-1">
        <Select
          tagRender={tagRender}
          defaultValue={defaultValue}
          placeholder={placeholder}
          size={size}
          options={options}
          loading={loading}
          disabled={disabled}
          onSearch={onSearch}
          searchValue={searchValue}
          optionFilterProp={optionFilterProp}
          filterOption={filterOption}
          showSearch={showSearch}
          notFoundContent={notFoundContent}
          onChange={onChange}
          mode={mode}
          allowClear={allowClear}
          defaultActiveFirstOption={defaultActiveFirstOption}
          onPopupScroll={onPopupScroll}
          autoClearSearchValue={false}
          onBlur={onBlur}
          onSelect={onSelect}
          onClear={onClear}
          popupMatchSelectWidth={false}
          value={watch && watch(name)}
          labelRender={labelRender}
        />
      </FormItem>
    </div>
  );
}
