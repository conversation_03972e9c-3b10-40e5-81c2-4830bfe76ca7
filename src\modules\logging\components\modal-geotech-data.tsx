import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { InputNumberCommon } from "@/components/common/input-number";
import { ModalCommon } from "@/components/common/modal-common";
import { SelectCommon } from "@/components/common/select-common";
import { useGetListGeotechSuite } from "@/modules/geotech-suite/hooks/useGetListGeotechSuite";
import { useGetListStructureCondition } from "@/modules/structure-condition/hooks/useGetListStructureCondition";
import { StructureSelectorType } from "@/modules/structure-type/enum/enum";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button, Form } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useCreateGeotechData } from "../hooks/useCreateGeotechData";
import { useUpdateGeotechData } from "../hooks/useUpdateGeotechData";
import {
  GeotechDataBody,
  GeotechDataBodyType,
} from "../model/geotech-data.model";
import {
  updateGeotechDataToEdit,
  updateMeasurePointsInterval,
  updateOpenModalGeotechData,
  updateRefetchGeotechData,
} from "../redux/loggingSlice/logging.slice";
export default function ModalGeotechData() {
  const { openModalGeotechData } = useAppSelector((state) => state.logging);
  const geotechDataToEdit = useAppSelector(
    (state) => state?.logging?.geotechDataToEdit
  );
  const dispatch = useAppDispatch();
  const { control, handleSubmit, setValue, reset } =
    useForm<GeotechDataBodyType>({
      resolver: zodResolver(GeotechDataBody),
    });
  const {
    request: requestCreateGeotechData,
    loading: loadingCreateGeotechData,
  } = useCreateGeotechData();
  const {
    request: requestUpdateGeotechData,
    loading: loadingUpdateGeotechData,
  } = useUpdateGeotechData();
  const onSubmit = (values: GeotechDataBodyType) => {
    if (geotechDataToEdit) {
      requestUpdateGeotechData(values, () => {
        dispatch(updateOpenModalGeotechData(false));
        dispatch(updateRefetchGeotechData());
        toast.success("Update geotech data successfully");
        reset();
        dispatch(updateGeotechDataToEdit(null));
      });
    } else {
      requestCreateGeotechData(values, () => {
        dispatch(updateOpenModalGeotechData(false));
        dispatch(updateRefetchGeotechData());
        toast.success("Create geotech data successfully");
        dispatch(updateGeotechDataToEdit(null));
        reset();
      });
    }
  };
  const structure = useAppSelector((state) => state.structure.detail);
  const geotechFieldId = structure?.id;
  useEffect(() => {
    if (geotechFieldId) {
      setValue("geotechFieldId", geotechFieldId);
    }
  }, [geotechFieldId]);
  const isPoint = structure?.selector === StructureSelectorType.Points;
  const hasOrientation = structure?.hasOrientation;
  const hasWidth = structure?.hasWidth;
  const hasCondition = structure?.hasCondition;
  const hasMineral = structure?.hasMineral;
  const { data: geotechSuite, request: requestGeotechSuite } =
    useGetListGeotechSuite();
  const [keywordGeotechSuite, setKeywordGeotechSuite] = useState("");
  useEffect(() => {
    requestGeotechSuite({ keyword: keywordGeotechSuite });
  }, [keywordGeotechSuite]);

  const x = useAppSelector((state) => state.logging.x);
  const depth = useAppSelector((state) => state.logging.depth);
  const imageCropId = useAppSelector((state) => state.logging.imageCropId);
  useEffect(() => {
    if (x && isPoint) {
      setValue("x", Number(x.toFixed(2)));
    }
    if (depth && isPoint) {
      setValue("depth", Number(depth.toFixed(2)));
    }
    if (imageCropId && isPoint) {
      setValue("imageCropId", imageCropId);
    }
  }, [x, depth, imageCropId]);
  const drillholeId = useAppSelector((state) => state.drillHole?.detail?.id);
  useEffect(() => {
    if (drillholeId) {
      setValue("drillHoleId", drillholeId);
    }
  }, [drillholeId]);

  useEffect(() => {
    if (geotechDataToEdit) {
      reset({
        ...geotechDataToEdit,
        x: Number(geotechDataToEdit?.x?.toFixed(2)),
        depth: Number(geotechDataToEdit?.depth?.toFixed(2)),
        depthTo: Number(geotechDataToEdit?.depthTo?.toFixed(2)),
        rockTypeId: geotechDataToEdit?.rockType?.id,
        structureConditionId: geotechDataToEdit?.structureCondition?.id,
      });
    }
  }, [geotechDataToEdit]);

  const { data: structureCondition, request: requestStructureCondition } =
    useGetListStructureCondition();
  const [keywordStructureCondition, setKeywordStructureCondition] =
    useState("");
  useEffect(() => {
    requestStructureCondition({ keyword: keywordStructureCondition });
  }, [keywordStructureCondition]);
  const measurePointsInterval = useAppSelector(
    (state) => state.logging.measurePointsInterval
  );
  useEffect(() => {
    if (measurePointsInterval?.start && !isPoint) {
      setValue(
        "depth",
        Number(measurePointsInterval?.start?.depth?.toFixed(2))
      );
      setValue("x", Number(measurePointsInterval?.start?.x?.toFixed(2)));
    }
    if (measurePointsInterval?.end && !isPoint) {
      setValue(
        "depthTo",
        Number(measurePointsInterval?.end?.depth?.toFixed(2))
      );
      setValue("xTo", Number(measurePointsInterval?.end?.x?.toFixed(2)));
      setValue("imageCropId", measurePointsInterval?.start?.imageCropId as any);
      setValue("imageCropIdTo", measurePointsInterval?.end?.imageCropId as any);
    }
  }, [measurePointsInterval?.start, measurePointsInterval?.end, isPoint]);
  const geotechSuiteId = useAppSelector(
    (state) => state.logging.loggingSuiteId
  );
  useEffect(() => {
    if (geotechSuiteId) {
      setValue("geotechSuiteId", geotechSuiteId);
    }
  }, [geotechSuiteId]);

  return (
    <ModalCommon
      open={openModalGeotechData}
      centered
      padding={0}
      footer={null}
      onCancel={() => {
        dispatch(updateOpenModalGeotechData(false));
        dispatch(updateGeotechDataToEdit(null));
        dispatch(updateMeasurePointsInterval({}));
        reset();
      }}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      <p className="font-bold text-2xl">Geotech Data</p>

      <Form
        onFinish={handleSubmit(onSubmit, (err) => {
          console.log(err);
        })}
        className="flex flex-col gap-2"
      >
        {isPoint ? (
          <InputNumberCommon
            name="depth"
            placeholder="Depth"
            control={control}
            label="Depth"
            disabled
          />
        ) : (
          <>
            <InputNumberCommon
              name="depth"
              placeholder="Depth From"
              control={control}
              label="Depth From"
            />
            <InputNumberCommon
              name="depthTo"
              placeholder="Depth To"
              control={control}
              label="Depth To"
            />
          </>
        )}

        {hasCondition && (
          <SelectCommon
            name="structureConditionId"
            options={structureCondition?.map((item) => ({
              label: item.name,
              value: item.id,
            }))}
            placeholder="Select Structure Condition"
            control={control}
            label="Structure Condition"
            onChange={(value) => {
              setKeywordStructureCondition("");
            }}
            onSearch={(value) => {
              setKeywordStructureCondition(value);
            }}
            onClear={() => {
              setKeywordStructureCondition("");
            }}
            searchValue={keywordStructureCondition}
            showSearch
            allowClear
            filterOption={false}
          />
        )}
        {hasMineral && (
          <SelectCommon
            name="rockTypeId"
            options={structure?.rockGroup?.rockTypes?.map((item) => ({
              label: item.name,
              value: item.id,
            }))}
            placeholder="Select Rock Type"
            control={control}
            label="Rock Type"
            filterOption={false}
            allowClear
          />
        )}

        {hasOrientation && (
          <>
            <InputNumberCommon
              name="alphaAngle"
              placeholder="Alpha Angle"
              control={control}
              label="Alpha Angle"
            />
            <InputNumberCommon
              name="betaAngle"
              placeholder="Beta Angle"
              control={control}
              label="Beta Angle"
            />
          </>
        )}
        {hasWidth && (
          <InputNumberCommon
            name="width"
            placeholder="Width"
            control={control}
            label="Width"
          />
        )}
        <Button
          type="primary"
          htmlType="submit"
          className="col-span-2"
          loading={loadingCreateGeotechData || loadingUpdateGeotechData}
        >
          {geotechDataToEdit ? "Update" : "Save"}
        </Button>
      </Form>
    </ModalCommon>
  );
}
