import { SearchOutlined } from "@ant-design/icons";
import { Input, Select } from "antd";
import React, { memo } from "react";
import { LoggingGridTopProps } from "./table-geology-types";

export const LoggingGridTop = memo<LoggingGridTopProps>(
  ({
    loggingSuiteIdParams,
    geologySuites,
    geotechSuites,
    loading,
    handleLoggingSuiteChange,
    handleSearch,
  }) => {
    return (
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Combined Suite Selection - matching main logging component */}
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium text-gray-700">
              Logging Suite:
            </span>
            <Select
              allowClear
              value={loggingSuiteIdParams}
              key={loggingSuiteIdParams}
              placeholder="Choose suite"
              className="min-w-[250px]"
              options={(geologySuites ?? [])
                .map((attribute: any) => ({
                  label: attribute.name,
                  value: `geologySuites-${attribute.id}`,
                }))
                .concat(
                  (geotechSuites ?? []).map((geotechSuite: any) => ({
                    label: geotechSuite.name,
                    value: `geotechSuites-${geotechSuite.id}`,
                  }))
                )}
              onChange={handleLoggingSuiteChange}
              disabled={loading}
            />
          </div>
        </div>

        {/* Search */}
        <div className="flex items-center gap-2">
          <SearchOutlined className="text-gray-500" />
          <Input
            placeholder="Search..."
            onChange={(e) => handleSearch(e.target.value)}
            className="w-64"
            allowClear
            disabled={loading}
          />
        </div>
      </div>
    );
  }
);

LoggingGridTop.displayName = "LoggingGridTop";
