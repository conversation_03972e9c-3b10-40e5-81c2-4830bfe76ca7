"use client";
export interface IPermissionDeniedProps {}
import { AppImages } from "@/common/configs";
import { Button } from "antd";
import Image from "next/image";
import { useRouter } from "next/navigation";

export default function AppPermissionDenied(props: IPermissionDeniedProps) {
  const router = useRouter();
  return (
    <div className="bg-white h-full flex justify-center items-center">
      <div>
        <Image
          src={AppImages.permissionDenied}
          alt="Access denied art"
          width={500}
          height={500}
        />
        <p className="text-24-28 font-bold text-center">Access Denied</p>
        <div className="mt-2">
          <p className="text-center">
            Seem you don't have permission to access this page or resource.
          </p>
          <p className="text-center">
            Please contact your administrator for more information.
          </p>
        </div>
        <div className="flex justify-center mt-3">
          <Button size="large" type="primary" onClick={() => router.push("/")}>
            Back to Homepage
          </Button>
          <Button
            size="large"
            type="primary"
            onClick={() => router.push("/login")}
            className="ml-2"
          >
            Back to Login
          </Button>
        </div>
      </div>
    </div>
  );
}
