import IStorage from '@/common/interfaces/storage/IStorage';
import { injectable } from 'inversify';

@injectable()
export class LocalStorage implements IStorage {
  setItem(key: string, value: string) {
    return localStorage.setItem(key, value);
  }
  getItem(key: string) {
    return localStorage.getItem(key);
  }
  removeItem(key: string) {
    localStorage.removeItem(key);
    return key;
  }
}
