"use client";
import GlobalStyles from "@/common/@share-components/@shared/AppGlobalStyles";
import { StoreProvider } from "@/common/vendors/redux/provider/StoreProvider";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { type ReactNode } from "react";
import { ToastContainer } from "react-toastify";

interface Props {
  children: ReactNode;
}
const queryClient = new QueryClient();
function AuthLayout({ children }: Props) {
  return (
    <GlobalStyles>
      <StoreProvider>
        <ToastContainer position="top-center" autoClose={1000} />
        <QueryClientProvider client={queryClient}>
          <main>{children}</main>
        </QueryClientProvider>
      </StoreProvider>
    </GlobalStyles>
  );
}

export default AuthLayout;
