import "reflect-metadata";
import { AppLogger } from "@/common/infrastructure/logger/app.logger";
import { HttpRequest } from "@/common/infrastructure/request/http/http.request";
import ILogger from "@/common/interfaces/logger/ILogger";
import IRequest from "@/common/interfaces/request/IRequest";
import { AxiosSymbol, ShareSymbol } from "@/common/interfaces/share.types";
import IStorage from "@/common/interfaces/storage/IStorage";
import axios, { AxiosInstance } from "axios";
import { Container } from "inversify";
import { LocalStorage } from "../infrastructure/storage/local.storage";

const appContainer = new Container();
// Bind interface to specific instance
appContainer.bind<ILogger>(ShareSymbol.ILogger).to(AppLogger);
appContainer.bind<IRequest>(ShareSymbol.IRequest).to(HttpRequest);
appContainer.bind<IStorage>(ShareSymbol.IStorage).to(LocalStorage);
appContainer.bind<AxiosInstance>(AxiosSymbol.Axios).toConstantValue(axios);

// Get instance to use
const appLogger = appContainer.get<ILogger>(ShareSymbol.ILogger);
const appRequest = appContainer.get<IRequest>(ShareSymbol.IRequest);
const appStorage = appContainer.get<IStorage>(ShareSymbol.IStorage);
const appFetch = appContainer.get<AxiosInstance>(AxiosSymbol.Axios);

export { appContainer, appFetch, appLogger, appRequest, appStorage };
