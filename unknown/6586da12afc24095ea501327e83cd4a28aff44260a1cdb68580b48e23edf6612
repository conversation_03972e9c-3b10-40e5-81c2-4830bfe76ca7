import { Input } from "antd";
import { TextAreaProps } from "antd/es/input/TextArea";
import { Controller, FieldValues, RegisterOptions } from "react-hook-form";

interface AppInputTextAreaProps extends Omit<TextAreaProps, "name"> {
  control: any;
  rules?:
    | Omit<
        RegisterOptions<FieldValues, any>,
        "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
      >
    | undefined;
  name: string;
  handleKeyDown?: (e: React.KeyboardEvent<HTMLTextAreaElement>) => void;
}

const AppInputTextArea = ({
  control,
  name,
  rules,
  handleKeyDown,
  ...textAreaProps
}: AppInputTextAreaProps) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => (
        <Input.TextArea
          {...field}
          {...textAreaProps}
          onKeyDown={handleKeyDown}
        />
      )}
    />
  );
};

export default AppInputTextArea;
