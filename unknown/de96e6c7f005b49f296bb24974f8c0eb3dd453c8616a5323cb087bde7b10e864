import { InputNumber, InputNumberProps } from "antd";
import { Controller, FieldValues, RegisterOptions } from "react-hook-form";

interface AppInputNumberProps extends Omit<InputNumberProps, "name"> {
  control: any;
  rules?:
    | Omit<
        RegisterOptions<FieldValues, any>,
        "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
      >
    | undefined;
  name: string;
}

const AppInputNumber = ({
  control,
  name,
  rules,
  precision = 2,
  decimalSeparator = ".",
  ...inputProps
}: AppInputNumberProps) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => (
        <InputNumber
          {...field}
          {...inputProps}
          precision={precision}
          decimalSeparator={decimalSeparator}
        />
      )}
    />
  );
};

export default AppInputNumber;
