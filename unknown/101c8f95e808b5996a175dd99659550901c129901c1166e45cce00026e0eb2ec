import { Input, InputProps } from "antd";
import { Controller, FieldValues, RegisterOptions } from "react-hook-form";

interface AppInputTextProps extends Omit<InputProps, "name"> {
  control: any;
  rules?:
    | Omit<
        RegisterOptions<FieldValues, any>,
        "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
      >
    | undefined;
  name: string;
}

const AppInputText = ({
  control,
  name,
  rules,
  ...inputProps
}: AppInputTextProps) => {
  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field }) => <Input {...field} {...inputProps} />}
    />
  );
};

export default AppInputText;
