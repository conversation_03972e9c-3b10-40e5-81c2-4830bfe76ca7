import { IRequestOptions } from "@/common/interfaces/request/IRequestOptions";

export default interface IRequest {
  get<T>(path: string, query?: object, options?: IRequestOptions): Promise<T>;
  post<T>(path: string, body?: object, options?: IRequestOptions): Promise<T>;
  put<T>(path: string, body?: object, options?: IRequestOptions): Promise<T>;
  patch<T>(path: string, body?: object, options?: IRequestOptions): Promise<T>;
  delete<T>(path: string, body?: object, options?: IRequestOptions): Promise<T>;
  upload<T>(
    path: string,
    appendFiles: { key: string; value: any }[],
    options?: IRequestOptions,
    onProgress?: (progress: number) => void
  ): Promise<T>;
}
