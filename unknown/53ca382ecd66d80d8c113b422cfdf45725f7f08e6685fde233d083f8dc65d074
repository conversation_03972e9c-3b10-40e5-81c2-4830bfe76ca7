import { Modal } from "antd";
import { UseFormReturn } from "react-hook-form";
import { DescriptionFill } from "./render-modal-fill/description-fill";
import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { FieldType } from "@/modules/geology-suite-field/const/enum";
import { ColorFill } from "./render-modal-fill/color-fill";
import { RenderFill } from "./render-modal-fill/render-fill";
import { RockGroupFill } from "./render-modal-fill/rock-group-fill";

export const ModalFill = ({
  selectedColumn,
  isModalOpen,
  form,
  setIsModalOpen,
}: {
  selectedColumn: string;
  isModalOpen: boolean;
  form: UseFormReturn<any>;
  setIsModalOpen: (isModalOpen: boolean) => void;
}) => {
  const { geologySuite } = useAppSelector((state) => state.importData);
  const geologySuiteFields = geologySuite?.geologySuiteFields?.find(
    (field) => field.name === selectedColumn
  );
  const type = geologySuiteFields?.geologyField?.type;
  console.log(type);
  const renderFill = (type: FieldType) => {
    switch (type) {
      case FieldType.Description:
        return (
          <DescriptionFill
            form={form}
            selectedColumn={selectedColumn}
            setIsModalOpen={setIsModalOpen}
          />
        );
      case FieldType.Colour:
        return (
          <ColorFill
            form={form}
            selectedColumn={selectedColumn}
            setIsModalOpen={setIsModalOpen}
          />
        );
      case FieldType.RockGroup:
        return (
          <RockGroupFill
            form={form}
            selectedColumn={selectedColumn}
            setIsModalOpen={setIsModalOpen}
          />
        );
      default:
        return (
          <RenderFill
            form={form}
            selectedColumn={selectedColumn}
            setIsModalOpen={setIsModalOpen}
          />
        );
    }
  };
  return (
    <Modal
      title={`Fill Empty Cells - ${selectedColumn}`}
      open={isModalOpen}
      footer={null}
      onCancel={() => {
        setIsModalOpen(false);
      }}
    >
      <div className="flex flex-col gap-4">
        <p>Enter value to fill empty cells in column "{selectedColumn}":</p>
        {renderFill(type)}
      </div>
    </Modal>
  );
};
