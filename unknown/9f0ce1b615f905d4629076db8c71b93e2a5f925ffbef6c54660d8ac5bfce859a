import { DatePicker, DatePickerProps } from "antd";
import dayjs from "dayjs";
import { Controller, FieldValues, RegisterOptions } from "react-hook-form";

interface AppInputDateSelectProps extends Omit<DatePickerProps, "name"> {
  control: any;
  rules?:
    | Omit<
        RegisterOptions<FieldValues, any>,
        "valueAsNumber" | "valueAsDate" | "setValueAs" | "disabled"
      >
    | undefined;
  name: string;
}

const AppInputDateSelect = ({
  control,
  name,
  rules,
  ...datePickerProps
}: AppInputDateSelectProps) => {
  return (
    <Controller
      control={control}
      name={name}
      render={({ field }) => (
        <DatePicker
          {...field}
          value={field.value ? dayjs(field.value) : null}
          onChange={(date) => {
            console.log("date", date);
            if (date) {
              field.onChange(date ? date.format("YYYY-MM-DD") : null);
            } else {
              field.onChange(undefined);
            }
          }}
        />
      )}
    />
  );
};

export default AppInputDateSelect;
