import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useDebounce } from "@/hooks/useDebounce";
import imageTypeRequest from "../api/image-type.api";

export const useQueryImageType = () => {
  const [searchParams, setSearchParams] = useState<any>({
    isActive: true,
    maxResultCount: 10,
    skipCount: 0,
  });
  const debouncedSearch = useDebounce(searchParams?.keyword);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["image-type", { ...searchParams, keyword: debouncedSearch }],
    queryFn: () =>
      imageTypeRequest.getList({
        ...searchParams,
        isActive: searchParams?.isActive ?? true,
        keyword: debouncedSearch,
      }),
    placeholderData: keepPreviousData,
  });
  return {
    data,
    isLoading,
    error,
    refetch,
    setSearchParams,
    searchParams,
  };
};
