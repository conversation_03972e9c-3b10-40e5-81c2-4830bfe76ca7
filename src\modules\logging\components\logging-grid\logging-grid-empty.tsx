import React from "react";
import { Empty } from "antd";

/**
 * Empty state component displayed when geology suite selection is missing
 * Shows a message indicating that geology suites need to be selected before proceeding
 */
export const LoggingGridEmpty: React.FC = () => {
  return (
    <div className="flex items-center justify-center h-full min-h-[400px] bg-white rounded-lg border border-gray-200">
      <Empty
        description={
          <div className="text-center">
            <h3 className="text-lg font-bold text-gray-700 mb-2 font-visby">
              No Geology Suite Selected
            </h3>
            <p className="text-gray-500 font-visby">
              Please select a geology suite from the dropdown menu on the left
              sidebar to view and edit logging data.
            </p>
          </div>
        }
        image={Empty.PRESENTED_IMAGE_SIMPLE}
        className="my-8"
      />
    </div>
  );
};

export default LoggingGridEmpty;
