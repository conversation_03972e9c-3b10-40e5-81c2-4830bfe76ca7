# Implementation Plan: Consolidating Multiple Canvases into a Single Canvas with Layers

## Problem Statement

Currently, the 3D view uses two separate WebGL canvases:

1. The main canvas for 3D visualization
2. A secondary canvas for the `FixedAxesHelper` component

Having multiple WebGL canvases on the same page is causing errors and potential performance issues. We need to consolidate them into a single canvas while maintaining the functionality where the axes helper stays fixed in screen space and shows proper world orientation.

## Solution Overview

We will implement a solution that renders the axes helper within the main canvas, positioned in a fixed corner relative to the camera viewport, while maintaining proper orientation to reflect the camera's rotation in world space.

## Technical Approach

### 1. Implementation Method: HUD (Heads-Up Display) Scene

We'll use a technique that implements two separate scenes within a single canvas:

1. **Main Scene**: The existing 3D scene with drillholes and other content
2. **HUD Scene**: A separate scene for the axes helper that:
   - Uses an orthographic camera
   - Renders after the main scene
   - Is positioned in screen space rather than world space
   - Maintains synchronization with the main camera's orientation

### 2. Architecture Diagram

```mermaid
graph TD
    MainCanvas["Single WebGL Canvas"]
    MainScene["Main Scene (3D View)"]
    HUDScene["HUD Scene (Fixed Axes)"]
    MainCamera["Perspective Camera"]
    HUDCamera["Orthographic Camera"]
    SceneContent["3D Content (Drillholes, etc.)"]
    AxesHelper["Axes Helper"]

    MainCanvas --> MainScene
    MainCanvas --> HUDScene

    MainScene --> MainCamera
    MainScene --> SceneContent

    HUDScene --> HUDCamera
    HUDScene --> AxesHelper

    MainCamera -- "Rotation Data" --> AxesHelper
```

### 3. Detailed Implementation Steps

#### Step 1: Create an `InCanvasFixedAxesHelper` Component

1. Create a new component that will replace the current `FixedAxesHelper`
2. This component will receive the main camera as a prop to synchronize orientation

```tsx
// InCanvasFixedAxesHelper.tsx
import React, { useRef } from "react";
import * as THREE from "three";
import { useFrame, useThree } from "@react-three/fiber";
import { Html } from "@react-three/drei";

const InCanvasFixedAxesHelper: React.FC<{ size: number }> = ({ size }) => {
  const axesRef = useRef<THREE.Group>(null);
  const { camera } = useThree();

  useFrame(() => {
    if (axesRef.current) {
      // Get the camera's rotation and apply inverse to keep axes orientation correct
      const quaternion = new THREE.Quaternion();
      camera.getWorldQuaternion(quaternion);
      axesRef.current.quaternion.copy(quaternion.invert());
    }
  });

  return (
    <group ref={axesRef} position={[-(size * 2), -(size * 2), 0]}>
      <axesHelper args={[size]} />

      {/* X-axis label */}
      <Html position={[size * 1.1, 0, 0]} center>
        <div
          style={{ color: "red", fontSize: size * 0.8, whiteSpace: "nowrap" }}
        >
          X (East)
        </div>
      </Html>

      {/* Y-axis label */}
      <Html position={[0, size * 1.1, 0]} center>
        <div
          style={{ color: "green", fontSize: size * 0.8, whiteSpace: "nowrap" }}
        >
          Y (North)
        </div>
      </Html>

      {/* Z-axis label */}
      <Html position={[0, 0, size * 1.1]} center>
        <div
          style={{ color: "blue", fontSize: size * 0.8, whiteSpace: "nowrap" }}
        >
          Z (Elevation)
        </div>
      </Html>
    </group>
  );
};

export default InCanvasFixedAxesHelper;
```

#### Step 2: Create a Screen-Space Overlay Component

```tsx
// ScreenSpaceOverlay.tsx
import React, { useRef } from "react";
import * as THREE from "three";
import { useFrame, useThree } from "@react-three/fiber";

interface ScreenSpaceOverlayProps {
  children: React.ReactNode;
  position: {
    x: "left" | "center" | "right";
    y: "top" | "center" | "bottom";
  };
  size: { width: number; height: number };
  distance?: number;
}

const ScreenSpaceOverlay: React.FC<ScreenSpaceOverlayProps> = ({
  children,
  position,
  size,
  distance = 10,
}) => {
  const { camera, viewport } = useThree();
  const groupRef = useRef<THREE.Group>(null);

  useFrame(() => {
    if (!groupRef.current) return;

    // Position the overlay in screen space
    const x =
      position.x === "left"
        ? -viewport.width / 2 + size.width / 2
        : position.x === "right"
        ? viewport.width / 2 - size.width / 2
        : 0;

    const y =
      position.y === "top"
        ? viewport.height / 2 - size.height / 2
        : position.y === "bottom"
        ? -viewport.height / 2 + size.height / 2
        : 0;

    // Position in front of the camera
    const vector = new THREE.Vector3(x, y, -distance);
    vector.unproject(camera);

    const dir = vector.sub(camera.position).normalize();
    const pos = camera.position.clone().add(dir.multiplyScalar(distance));

    groupRef.current.position.copy(pos);
    groupRef.current.quaternion.copy(camera.quaternion);
  });

  return <group ref={groupRef}>{children}</group>;
};

export default ScreenSpaceOverlay;
```

#### Step 3: Create a HUD Component for Rendering in Screen Space

```tsx
// HUDScene.tsx
import React from "react";
import { createPortal, useThree } from "@react-three/fiber";
import * as THREE from "three";
import InCanvasFixedAxesHelper from "./InCanvasFixedAxesHelper";

const HUDScene: React.FC<{ size: number }> = ({ size }) => {
  const { gl, camera, scene } = useThree();

  // Create a separate scene for HUD elements
  const hudScene = new THREE.Scene();

  // Create an orthographic camera for the HUD
  const hudCamera = new THREE.OrthographicCamera(-1, 1, 1, -1, 0.1, 1000);
  hudCamera.position.z = 10;

  // Use createPortal to render to a different scene
  return createPortal(<InCanvasFixedAxesHelper size={size} />, hudScene);
};

export default HUDScene;
```

#### Step 4: Integrate into Main Canvas in 3d-view.tsx

1. Remove the separate `FixedAxesHelper` component outside the Canvas
2. Add the `HUDScene` component inside the Canvas after the main scene content

```tsx
// Modify 3d-view.tsx
<Canvas
  camera={{ position: [-70, 70, 250], fov: 50 }}
  shadows
  gl={{ antialias: true, preserveDrawingBuffer: true }}
>
  {/* ... existing content ... */}

  {/* Replace the external FixedAxesHelper with the in-canvas version */}
  <ScreenSpaceOverlay
    position={{ x: "left", y: "bottom" }}
    size={{ width: 170, height: 120 }}
    distance={10}
  >
    <InCanvasFixedAxesHelper size={40} />
  </ScreenSpaceOverlay>
</Canvas>
```

## Alternative Approach: React-Three-Fiber View Implementation

React-Three-Fiber also provides a `View` component that lets you create multiple views within a single canvas. This might be an even cleaner approach:

```tsx
<Canvas frameloop="always">
  <View track={mainViewRef}>{/* Main scene content */}</View>

  <View track={axesViewRef}>{/* Axes helper content */}</View>
</Canvas>
```

## Expected Benefits

1. **Resolved Errors**: Eliminates conflicts from multiple WebGL contexts
2. **Better Performance**: Reduces overhead by using a single WebGL context
3. **Maintained Functionality**: Axes helper remains fixed in screen space
4. **Proper Orientation**: Axes continue to reflect world orientation correctly
5. **Cleaner Code**: Eliminates hacks like global window variables for sharing state

## Potential Challenges and Solutions

1. **Z-Index and Rendering Order**:

   - Challenge: Ensuring the axes helper renders on top of the main scene
   - Solution: Using proper rendering order and potentially using stencil buffers

2. **Positioning Accuracy**:

   - Challenge: Screen-space positioning may not perfectly match the current CSS-based approach
   - Solution: Fine-tuning the positioning logic and scaling

3. **Performance Impact**:
   - Challenge: Additional calculations for screen-space positioning
   - Solution: Optimizing the calculations and potentially reducing update frequency

## Testing Strategy

1. Test with multiple drillholes to ensure performance is maintained
2. Verify axes orientation correctly matches camera rotation
3. Confirm axes remain fixed in screen space during camera movement
4. Test on different screen sizes to ensure proper scaling and positioning
