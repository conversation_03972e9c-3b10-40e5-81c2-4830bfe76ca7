import { PERMISSIONS } from "@/constants/general.const";
import PermissionProvider from "@/components/layout/permission-provider";
import ChangePassword from "@/modules/auth/components/change-password";

export default function ChangePasswordPage() {
  return (
    <PermissionProvider
      permissions={[
        PERMISSIONS.Admin,
        PERMISSIONS.Company,
        PERMISSIONS.EditorUser,
        PERMISSIONS.ViewerUser,
      ]}
    >
      <ChangePassword />
    </PermissionProvider>
  );
}
