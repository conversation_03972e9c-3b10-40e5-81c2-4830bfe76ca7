import { useAppSelector } from "@/common/vendors/redux/store/hook";
import { useDebounce } from "@/hooks/useDebounce";
import { keepPreviousData, useQuery } from "@tanstack/react-query";
import { useCallback, useEffect, useMemo, useState } from "react";
import geotechSuiteRequest from "../api/geotech-suite.api";
import { GeotechSuiteQuery } from "../interface/geotech-suite.query";

interface ScrollEvent {
  target: {
    scrollTop: number;
    offsetHeight: number;
    scrollHeight: number;
  };
}

export const useQueryGeotechSuite = () => {
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );

  const [enable, setEnable] = useState(false);
  const [isEnd, setIsEnd] = useState(false);
  const [searchParams, setSearchParams] = useState<GeotechSuiteQuery>({
    isActive: true,
    maxResultCount: 10,
    skipCount: 0,
  });

  // Memoize search params to prevent unnecessary re-renders
  const memoizedSearchParams = useMemo(
    () => searchParams,
    [
      searchParams.isActive,
      searchParams.maxResultCount,
      searchParams.skipCount,
      searchParams.keyword,
      searchParams.projectId,
    ]
  );

  // Throttled scroll handler with proper pagination
  const handleScroll = useCallback(
    (event: ScrollEvent) => {
      const target = event.target;
      const isAtBottom =
        target.scrollTop + target.offsetHeight >= target.scrollHeight - 1;

      if (isAtBottom && !isEnd) {
        setSearchParams((prev) => ({
          ...prev,
          maxResultCount: (prev.maxResultCount ?? 0) + 10,
        }));
      }
    },
    [isEnd]
  );

  // Update project and prospect IDs when they change (fixed infinite re-render)
  useEffect(() => {
    setSearchParams((prev) => ({
      ...prev,
      projectId: globalProjectId,
    }));
  }, [globalProjectId]);

  // Reset pagination when keyword changes (fixed dependency issue)
  useEffect(() => {
    setIsEnd(false);
    setSearchParams((prev) => ({
      ...prev,
      skipCount: 0,
      maxResultCount: 10,
    }));
  }, [searchParams.keyword]);

  const debouncedSearch = useDebounce(searchParams?.keyword);

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: [
      "geotech-suites",
      { ...memoizedSearchParams, keyword: debouncedSearch },
    ],
    queryFn: () =>
      geotechSuiteRequest.getList({
        ...memoizedSearchParams,
        keyword: debouncedSearch,
      }),
    // enabled: enable && !isEnd,
    placeholderData: keepPreviousData,
  });

  // Improved end detection logic
  useEffect(() => {
    if (data?.data?.pagination) {
      const { total } = data.data.pagination;
      const currentCount = searchParams.maxResultCount ?? 0;

      if (currentCount >= total) {
        setIsEnd(true);
      }
    }
  }, [data, searchParams.maxResultCount]);

  return {
    data,
    isLoading,
    error,
    refetch,
    setSearchParams,
    searchParams: memoizedSearchParams,
    setEnable,
    handleScroll,
    isEnd,
    setIsEnd,
  };
};
