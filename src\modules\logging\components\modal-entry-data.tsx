import { ButtonCommon } from "@/components/common/button-common";
import { ModalCommon } from "@/components/common/modal-common";
import { toast } from "react-toastify";
import { useDeleteDataEntry } from "../hooks/useDeleteDataEntry";
import { ModalCreateEntry } from "./modal-geology-suite";
import { Button } from "antd";

export default function ModalEntryData({
  modalState,
  setModalState,
  refetchDataEntry,
}: {
  modalState: any;
  setModalState: (value: any) => void;
  refetchDataEntry: (isSaveLine?: boolean, drillholeId?: number) => void;
}) {
  const { request: requestDelete, loading: loadingDelete } =
    useDeleteDataEntry();
  const handleDelete = () => {
    requestDelete(
      { id: modalState.detail.id },
      () => {
        setModalState({ ...modalState, isOpen: false });
        refetchDataEntry();
      },
      () => {
        toast.error("Delete entry failed");
      }
    );
  };
  return (
    <ModalCommon
      open={modalState.isOpen}
      centered
      padding={0}
      footer={null}
      onCancel={() => setModalState({ ...modalState, isOpen: false })}
      style={{ borderRadius: 8 }}
      width={450}
      closable={false}
    >
      {modalState.type === "delete" ? (
        <>
          <p className="text-24-24 font-semibold my-2">
            Are you sure you want to delete this entry?
          </p>
          <div className="flex justify-end gap-2">
            <Button
              onClick={() => setModalState({ ...modalState, isOpen: false })}
            >
              Cancel
            </Button>
            <Button
              onClick={handleDelete}
              loading={loadingDelete}
              type="primary"
            >
              Delete
            </Button>
          </div>
        </>
      ) : (
        <ModalCreateEntry
          modalState={modalState}
          setModalState={setModalState}
          refetchDataEntry={refetchDataEntry}
        />
      )}
    </ModalCommon>
  );
}
