import React, { memo, useCallback, useState } from "react";
import { Select } from "antd";
import { Control, Controller } from "react-hook-form";
import { ErrorTooltip } from "./error-tooltip";

interface PicklistOption {
  id: number;
  name: string;
  value?: any;
}

interface FieldPicklistProps {
  control: Control<any>;
  name: string;
  disabled?: boolean;
  mandatory?: boolean;
  options?: PicklistOption[];
  onKeyDown?: (event: React.KeyboardEvent) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  className?: string;
  id?: string;
  onFieldChange?: (rowIndex: number, fieldPath: string, value: any) => void;
  rowIndex?: number;
  fieldPath?: string;
}

export const FieldPicklist = memo<FieldPicklistProps>(
  ({
    control,
    name,
    disabled = false,
    mandatory = false,
    options = [],
    onKeyDown,
    onFocus,
    onBlur,
    className,
    id,
    onFieldChange,
    rowIndex,
    fieldPath,
  }) => {
    const [searchValue, setSearchValue] = useState("");

    const handleKeyDown = useCallback(
      (event: React.KeyboardEvent) => {
        // Handle tab navigation
        if (event.key === "Tab") {
          onKeyDown?.(event);
        }
      },
      [onKeyDown]
    );

    const handleFocus = useCallback(() => {
      onFocus?.();
    }, [onFocus]);

    const handleBlur = useCallback(() => {
      onBlur?.();
    }, [onBlur]);

    // Create sorted options that prioritize entries beginning with search input
    const createSortedOptions = useCallback(
      (searchValue: string = "") => {
        const inputLower = searchValue.toLowerCase();

        return options
          .filter((option) => {
            const nameLower = option.name?.toLowerCase() || "";
            return nameLower.includes(inputLower);
          })
          .map((option) => ({
            label: option.name,
            value: option.id,
            originalOption: option,
          }))
          .sort((a, b) => {
            const nameA = a.originalOption.name?.toLowerCase() || "";
            const nameB = b.originalOption.name?.toLowerCase() || "";

            // Check if entries start with the search input
            const aStartsWith = nameA.startsWith(inputLower);
            const bStartsWith = nameB.startsWith(inputLower);

            // Prioritize entries that start with the search input
            if (aStartsWith && !bStartsWith) return -1;
            if (!aStartsWith && bStartsWith) return 1;

            // If both start with or both don't start with, sort alphabetically
            return nameA.localeCompare(nameB);
          });
      },
      [options]
    );

    const selectOptions = createSortedOptions(searchValue);

    return (
      <Controller
        name={name}
        control={control}
        rules={{
          required: mandatory ? "This field is required" : false,
        }}
        render={({ field, fieldState: { error } }) => (
          <div className="w-full">
            <Select
              {...field}
              id={id}
              disabled={disabled}
              placeholder="Select option"
              className={`w-full ${className || ""}`}
              options={selectOptions}
              allowClear
              showSearch
              virtual={selectOptions.length > 100}
              onSearch={(value) => {
                setSearchValue(value);
              }}
              filterOption={false}
              onKeyDown={handleKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onChange={(value) => {
                field.onChange(value);

                // Trigger row status update
                if (
                  onFieldChange &&
                  typeof rowIndex === "number" &&
                  fieldPath
                ) {
                  onFieldChange(rowIndex, fieldPath, value);
                }
              }}
            />
            <ErrorTooltip error={error} />
          </div>
        )}
      />
    );
  }
);

FieldPicklist.displayName = "FieldPicklist";
