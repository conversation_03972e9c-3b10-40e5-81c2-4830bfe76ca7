import z from "zod";

export const ProcessBatchBody = z.object({
  id: z.any().optional(),
  name: z.string().trim().min(1, { message: "Required" }),
  description: z.string().optional(),
  projectId: z.number(),
  prospectId: z.number(),
  drillholeId: z.number(),
  workflowId: z.number(),
  statusFilter: z.number().optional().nullable(),
  statusDone: z.number().optional().nullable(),
  imageCategory: z.number().optional().nullable(),
  imageTypeId: z.number().optional().nullable(),
  imageSubTypeId: z.number().optional().nullable(),
});

export type ProcessBatchBodyType = z.TypeOf<typeof ProcessBatchBody>;
