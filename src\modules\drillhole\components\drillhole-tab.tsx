import { SortOrder } from "@/common/interfaces/general/general.types";
import { Button<PERSON>ommon } from "@/components/common/button-common";
import { InputNumberCommon } from "@/components/common/input-number";
import { InputTextCommon } from "@/components/common/input-text";
import { SelectCommon } from "@/components/common/select-common";
import { ToogleCommon } from "@/components/common/toogle-common";
import { useGetListProject } from "@/modules/projects/hooks";
import { useGetListProspect } from "@/modules/prospect/hooks/useGetListProspect";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, Spin } from "antd";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { useUpdateDrillHole } from "../hooks/useUpdateDrillHole.hook";
import { drillHoleStatusOptions } from "../model/enum/drillhole.enum";
import {
  DrillHoleBody,
  DrillHoleBodyType,
} from "../model/schema/drillhole.schema";

export interface IDrillholeTabProps {
  drillhole: any;
  refetch: () => void;
}

export function DrillholeTab({ drillhole, refetch }: IDrillholeTabProps) {
  if (!drillhole) return null;
  console.log(drillhole);

  const { control, handleSubmit, watch, setValue, reset } =
    useForm<DrillHoleBodyType>({
      resolver: zodResolver(DrillHoleBody),
      // defaultValues: {
      //   name: drillhole?.name,
      //   rl: drillhole?.rl,
      //   maxDepth: drillhole?.maxDepth,
      //   drillHoleStatus: drillhole?.drillHoleStatus,
      //   isActive: drillhole?.isActive,
      //   elevation: drillhole?.elevation,
      //   northing: drillhole?.northing,
      //   easting: drillhole?.easting,
      //   dip: drillhole?.dip,
      //   azimuth: drillhole?.azimuth,
      // },
    });
  useEffect(() => {
    reset({
      ...drillhole,
      projectId: drillhole.project?.id,
      prospectId: drillhole.prospect?.id,
    });
  }, [drillhole]);
  const { request: requestUpdateDrillHole, loading: loadingUpdateDrillhole } =
    useUpdateDrillHole();
  const onSubmit = (values: any) => {
    requestUpdateDrillHole(
      {
        ...(values as any),
        id: drillhole.id,
      },
      () => {
        toast.success("Update drillhole successfully");
        refetch();
      },
      () => {}
    );
  };
  const {
    data: projects,
    request: getListProjects,
    loading: loadingListProject,
  } = useGetListProject();
  const [maxResultCountProject, setMaxResultCountProject] = useState(10);
  const [keywordProject, setKeywordProject] = useState<any>();

  const handleScrollProject = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProject(maxResultCountProject + 10);
    }
  };
  useEffect(() => {
    getListProjects({
      maxResultCount: maxResultCountProject,
      skipCount: 0,
      sortField: "name",
      sortOrder: SortOrder.ASC,
      keyword: keywordProject,
    });
  }, [maxResultCountProject, keywordProject]);
  const {
    data: prospects,
    request: getListProspects,
    loading: loadingListProspect,
  } = useGetListProspect();
  const [maxResultCountProspect, setMaxResultCountProspect] = useState(10);
  const [keywordProspect, setKeywordProspect] = useState<any>();
  const selectedProjectId = watch("projectId");

  useEffect(() => {
    getListProspects({
      maxResultCount: maxResultCountProspect,
      skipCount: 0,
      projectIds: selectedProjectId ? [selectedProjectId] : undefined,
      keyword: keywordProspect,
    });
  }, [maxResultCountProspect, keywordProspect, selectedProjectId]);
  const handleScrollProspect = (event: any) => {
    const target = event.target;
    if (target.scrollTop + target.offsetHeight === target.scrollHeight) {
      setMaxResultCountProspect(maxResultCountProspect + 10);
    }
  };
  const handleDiscard = () => {
    setValue("projectId", drillhole.projectId);
    setValue("prospectId", drillhole.prospectId);
    setValue("name", drillhole.name);
    setValue("rl", drillhole.rl);
    setValue("maxDepth", drillhole.maxDepth);
    setValue("drillHoleStatus", drillhole.drillHoleStatus);
    setValue("isActive", drillhole.isActive);
    setValue("elevation", drillhole.elevation);
    setValue("northing", drillhole.northing);
    setValue("easting", drillhole.easting);
    setValue("dip", drillhole.dip);
    setValue("azimuth", drillhole.azimuth);
  };
  return (
    <>
      <Form onFinish={handleSubmit(onSubmit)} className="gap-3">
        <div className="flex flex-col gap-3 mt-5">
          <InputTextCommon
            label="Name"
            name="name"
            placeholder="Type drillhole name here"
            control={control}
            autoComplete="off"
          />

          <SelectCommon
            control={control}
            label="Project"
            name="projectId"
            onSearch={(value) => setKeywordProject(value)}
            onChange={(value) => {
              getListProspects({
                maxResultCount: maxResultCountProspect,
                skipCount: 0,
                projectIds: value ? [value] : undefined,
                sortField: "name",
                sortOrder: SortOrder.ASC,
              });
              setValue("prospectId", undefined);
            }}
            searchValue={keywordProject}
            onBlur={() => {
              setKeywordProject("");
              getListProjects({
                maxResultCount: maxResultCountProject,
                skipCount: 0,
                sortField: "name",
                sortOrder: SortOrder.ASC,
              });
            }}
            options={projects.map((project) => ({
              label: project.name,
              value: project.id,
            }))}
            notFoundContent={
              loadingListProject ? <Spin size="small" /> : <>Not found</>
            }
            filterOption={false} // disable client-side filtering
            showSearch
            placeholder="Select project"
            onPopupScroll={handleScrollProject}
          />
          <SelectCommon
            disabled={!selectedProjectId}
            control={control}
            label="Prospect"
            name="prospectId"
            onSearch={(value) => {
              setKeywordProspect(value);
            }}
            searchValue={keywordProspect}
            onBlur={() => {
              setKeywordProspect("");
              getListProspects({
                maxResultCount: maxResultCountProspect,
                skipCount: 0,
                sortField: "name",
                sortOrder: SortOrder.ASC,
              });
            }}
            options={prospects.map((prospect) => ({
              label: prospect.name,
              value: prospect.id,
            }))}
            notFoundContent={
              loadingListProspect ? <Spin size="small" /> : <>Not found</>
            }
            filterOption={false} // disable client-side filtering
            showSearch
            placeholder="Select prospect"
            onPopupScroll={handleScrollProspect}
          />

          <InputNumberCommon
            label="Maximum Depth"
            name="maxDepth"
            placeholder="Type maximum depth here (decimal)"
            control={control}
          />
          <div className="flex gap-2">
            <InputNumberCommon
              label="Elevation"
              name="elevation"
              placeholder="Elevation "
              control={control}
            />
            <InputNumberCommon
              label="Northing"
              name="northing"
              placeholder="Northing"
              control={control}
            />
            <InputNumberCommon
              label="Easting"
              name="easting"
              placeholder="Easting"
              control={control}
            />
            <InputNumberCommon
              label="Dip"
              name="dip"
              placeholder="Dip"
              control={control}
            />
            <InputNumberCommon
              label="Azimuth"
              name="azimuth"
              placeholder="Azimuth"
              control={control}
            />
          </div>

          <SelectCommon
            control={control}
            label="Status"
            name="drillHoleStatus"
            options={drillHoleStatusOptions}
            placeholder="Select drillhole status"
          />
          <ToogleCommon label="Active" name="isActive" control={control} />
        </div>

        <div className="flex gap-3 mt-3 ">
          <ButtonCommon
            loading={loadingUpdateDrillhole}
            type="submit"
            className="btn btn-sm  hover:bg-primary-hover bg-primary text-white border-none"
          >
            Save Details
          </ButtonCommon>
          <ButtonCommon
            onClick={handleDiscard}
            className="btn py-2 btn-sm bg-slate-400 text-white border-none hover:bg-slate-500"
          >
            Reset Values
          </ButtonCommon>
        </div>
      </Form>
    </>
  );
}
