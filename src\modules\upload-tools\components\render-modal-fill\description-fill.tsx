import { useEffect, useState } from "react";
import { UseFormReturn } from "react-hook-form";
import { Button, Input } from "antd";
import TextArea from "antd/es/input/TextArea";

export const DescriptionFill = ({
  form,
  setIsModalOpen,
  selectedColumn,
}: {
  form: UseFormReturn<any>;
  setIsModalOpen: (isModalOpen: boolean) => void;
  selectedColumn: string;
}) => {
  const [fillValue, setFillValue] = useState("");
  const handleFillColumn = (keyName: string, value: any) => {
    form.watch("dataUpload").forEach((d: any, index: number) => {
      if (d[keyName] === "") {
        form.setValue(`dataUpload.${index}.${keyName}`, value);
      }
    });
    setIsModalOpen(false);
  };
  useEffect(() => {
    setFillValue("");
  }, []);
  return (
    <div className="flex flex-col gap-4">
      <TextArea
        value={fillValue}
        onChange={(e) => setFillValue(e.target.value)}
        placeholder="Enter value"
      />
      <Button
        onClick={() => handleFillColumn(selectedColumn, fillValue)}
        disabled={!fillValue}
      >
        Fill Description
      </Button>
    </div>
  );
};
