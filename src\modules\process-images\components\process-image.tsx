"use client";
import {
  useAppDispatch,
  useAppSelector,
} from "@/common/vendors/redux/store/hook";
import { useGetListImage } from "@/modules/image/hooks/useGetListImage";
import { useGetDetailProject } from "@/modules/projects/hooks/useGetDetailProject";
import { Button } from "antd";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import { FaArrowLeft, FaArrowRight } from "react-icons/fa6";
import {
  setPolygons,
  setPolygonsDetail,
} from "../redux/processImageSlice/process-image.slice";
import DrawImage from "./draw-image";
import { ImageInfo } from "./image-info";
import { NavigationImage } from "./navigation-image";
import ProcessImagePanel from "./process-image-panel";

export function ProcessImageView({
  isAdvance = true,
}: {
  isAdvance?: boolean;
}) {
  const router = useRouter();
  const dispatch = useAppDispatch();
  const globalProjectId = useAppSelector(
    (state) => state.user?.userInfo?.projectId
  );
  const globalProspectId = useAppSelector(
    (state) => state.user.userInfo?.prospectId
  );
  const [refetchDrillhole, setRefetchDrillhole] = useState(false);
  const [originOCRdata, setOriginOCRdata] = useState<any[]>([]);
  //REQUEST HOOK
  const {
    request: requestGetListImage,
    total,
    data: image,
  } = useGetListImage();

  const { request: requestGetDetailProject } = useGetDetailProject();
  //get all query params
  const searchParams = useSearchParams();
  let querySearch: any;
  searchParams.forEach((value, key) => {
    querySearch = {
      ...querySearch,
      [key]: value,
    };
  });

  const [queryFilter, setQueryFilter] = useState<any>({
    projectId: querySearch?.projectId
      ? Number(querySearch.projectId)
      : querySearch?.projectId,
    prospectId: querySearch?.prospectId
      ? Number(querySearch.prospectId)
      : querySearch?.prospectId,
    drillholeId: querySearch?.drillholeId
      ? Number(querySearch.drillholeId)
      : querySearch?.drillholeId,
    imageStatus: querySearch?.imageStatus
      ? Number(querySearch.imageStatus)
      : undefined,
    depthFrom: querySearch?.depthFrom
      ? Number(querySearch.depthFrom)
      : querySearch?.depthFrom,
    depthTo: querySearch?.depthTo
      ? Number(querySearch.depthTo)
      : querySearch?.depthTo,
    skipCount: querySearch?.skipCount ?? 0,
    maxResultCount: 1,
    drillHoleStatus: querySearch?.drillHoleStatus
      ? Number(querySearch.drillHoleStatus)
      : querySearch?.drillHoleStatus,
    imageTypeId: querySearch?.imageTypeId
      ? Number(querySearch.imageTypeId)
      : querySearch?.imageTypeId,
    imageSubTypeId: querySearch?.imageSubTypeId
      ? Number(querySearch.imageSubTypeId)
      : querySearch?.imageSubTypeId,
  });
  useEffect(() => {
    // Delete params with drillholeId
    const param = new URLSearchParams(searchParams);

    for (const key of param.keys()) {
      if (key.startsWith("drillholeId")) {
        param.delete(key);
      }
    }

    // Update the URL with the new params
    router.replace(`${window.location.pathname}?${param.toString()}`);
  }, [globalProspectId]);
  const [fractures, setFractures] = useState<any[]>([]);
  const refetch = (
    onSuccess?: Function,
    onError?: Function,
    isAlign = false
  ) => {
    if (
      queryFilter.projectId &&
      queryFilter.prospectId &&
      queryFilter.drillholeId
    ) {
      requestGetListImage(
        {
          ...queryFilter,
          projectIds: [queryFilter.projectId],
          prospectIds: [queryFilter.prospectId],
          holeIds: [queryFilter.drillholeId],
        },
        (res: any) => {
          onSuccess && onSuccess(res);

          //SHOW IMAGE ROW
          const _croppedImages = res?.items[0]?.croppedImages ?? [];
          let croppedImagesRow: any[] = [];
          let croppedImagesBox: any[] = [];
          croppedImagesRow = _croppedImages
            ?.map((item: any) => {
              return {
                ...item,
                coordinate: JSON.parse(item.coordinate),
              };
            })
            .filter((item: any) => item.type?.toLowerCase() === "row");

          croppedImagesBox = _croppedImages
            ?.map((item: any) => {
              return {
                ...item,
                coordinate: JSON.parse(item.coordinate),
              };
            })
            .filter((item: any) => item.type?.toLowerCase() === "box");

          setImageRowList(croppedImagesRow);
          setImageBoxList(croppedImagesBox);

          //GET BLOCK ANNOTATION
          const BoundingBox = res?.items[0]?.boundingBox ?? "[]";
          const BoundingRow = res?.items[0]?.boundingRows ?? "[]";
          let polyAnnoBox = JSON.parse(BoundingBox.toLowerCase()) ?? [];
          let polyAnnoRow = JSON.parse(BoundingRow.toLowerCase()) ?? [];
          if (isAlign) {
          } else {
            setImgPolyValue([...polyAnnoBox, ...polyAnnoRow]);
          }

          //GET FRACTURES
          const fractures = res?.items[0]?.fractures ?? "[]";
          const fracturesParsed = JSON.parse(fractures) ?? [];
          setFractures(fracturesParsed);

          //GET OCR
          try {
            let ocrResult = res?.items[0]?.ocrResult?.toLowerCase() ?? "[]";
            ocrResult = JSON.parse(ocrResult)!;
            if (!ocrResult.length) {
              setOCRdata([]);
            } else {
              setOCRdata(ocrResult);
            }
          } catch (error) {
            console.log("error: ", error);
          }

          //GET SEGMENT
          try {
            let parsedSegment = (
              res?.items[0]?.segmentResult as string
            )?.includes("[")
              ? res?.items[0]?.segmentResult
              : "[]";
            querySearch;
            parsedSegment = JSON.parse(parsedSegment)!;
            if (!parsedSegment.length) {
              dispatch(setPolygons([]));
            } else {
              dispatch(setPolygons(parsedSegment));
            }
          } catch (error) {
            console.log("error: ", error);
          }
          //GET SEGMENT DETAIL
          try {
            let parsedSegmentDetail = (
              res?.items[0]?.segmentDetailResult as string
            )?.includes("[")
              ? res?.items[0]?.segmentDetailResult
              : "[]";
            querySearch;
            parsedSegmentDetail = JSON.parse(parsedSegmentDetail)!;
            if (!parsedSegmentDetail.length) {
              dispatch(setPolygonsDetail([]));
            } else {
              dispatch(setPolygonsDetail(parsedSegmentDetail));
            }
          } catch (error) {
            console.log("error: ", error);
          }
        },
        onError
      );
    }
  };

  useEffect(() => {
    refetch();
  }, [queryFilter]);

  //IMAGE STATE

  const [imageConfig, setImageConfig] = useState({
    OCR: false,
    filterDirectOCR: false,
    directOCR: false,
    rectangleShowList: [],
    isPanning: false,
    showPolygonGroup: false,
    addDot: false,
    removeDot: false,
    cutting: false,
    showFractures: false,
  });

  //DRAW BLOCK
  const [annotations, setAnnotations] = useState<any[]>([]);
  const [polyValue, setPolyValue] = useState<any>({});
  const [imgPolyValue, setImgPolyValue] = useState<any>([]);
  const [selectedRowPolygonId, setSelectedRowPolygonId] = useState<any>();
  const [selectedBoxPolygonId, setSelectedBoxPolygonId] = useState<any>();
  const [optionBoundingRow, setOptionBoundingRow] = useState<any[]>([]);
  const [optionBoundingBox, setOptionBoundingBox] = useState<any[]>([]);
  const [showList, setShowList] = useState("Original");
  const [OCRdata, setOCRdata] = useState<any>([]);
  const [directOCRdata, setDirectOCRdata] = useState<any[]>([]);

  //BREAK SEGMENT
  const [breakPoints, setBreakPoints] = useState<{
    firstPoint: { point: number[]; idx: number };
    secondPoint: { point: number[]; idx: number };
    isComplete: boolean;
  }>({
    firstPoint: {
      point: [0, 0],
      idx: -1,
    },
    secondPoint: {
      point: [0, 0],
      idx: -1,
    },
    isComplete: true,
  });

  const [selectedGroupPolygon, setSelectedGroupPolygon] = useState<any>();

  //GET LIST POLYGON OPTIONS
  const fetchListPolygon = (projectId: any) => {
    if (!projectId) return;
    requestGetDetailProject(projectId, (res: any) => {
      const boundingBoxs = res.boundingBoxs ?? [];
      const boundingRows = res.boundingRows ?? [];
      const rowPoly = boundingRows.map((item: any) => {
        return {
          coordinates: item.coordinates,
          value: item.id,
          label: item.name,
        };
      });

      const boxPoly = boundingBoxs.map((item: any) => {
        return {
          coordinates: item.coordinates,
          value: item.id,
          label: item.name,
        };
      });
      setOptionBoundingBox(boxPoly);
      setOptionBoundingRow(rowPoly);
    });
  };

  useEffect(() => {
    if (!image?.project?.id) return;
    fetchListPolygon(image?.project?.id);
  }, [image?.project?.id]);

  useEffect(() => {
    const rowId = selectedRowPolygonId?.value;
    const row = optionBoundingRow.find((item) => item.value === rowId);
    setSelectedRowPolygonId(row);
    const boxId = selectedBoxPolygonId?.value;
    const box = optionBoundingBox.find((item) => item.value === boxId);
    setSelectedBoxPolygonId(box);
  }, [optionBoundingBox, optionBoundingRow]);

  //IF USER DONT CHOOSE BLOCK, SHOW ITS OWN BLOCK
  useEffect(() => {
    if (!polyValue.row && !polyValue.box) {
      setAnnotations(imgPolyValue);
    }
  }, [imgPolyValue, polyValue]);

  //DRAW BLOCK ACCORDING TO USER CHOOSE
  useEffect(() => {
    if (polyValue.row && !polyValue.box) {
      // show anno according to polyValue.row and imgPolyValue
      const corRow = JSON.parse(polyValue.row.toLowerCase())! ?? [];
      const itsBox = imgPolyValue.filter((item: any) => {
        return item?.type === "box";
      });
      setAnnotations([...corRow, ...itsBox]);
    }
    if (polyValue.row && polyValue.box) {
      // show anno according to polyValue.row and polyValue.box
      const corBox = JSON.parse(polyValue.box.toLowerCase())! ?? [];
      const corRow = JSON.parse(polyValue.row.toLowerCase())! ?? [];
      setAnnotations([...corBox, ...corRow]);
    }
    if (!polyValue.row && polyValue.box) {
      const corBox = JSON.parse(polyValue.box.toLowerCase())! ?? [];
      const itsRow = imgPolyValue.filter((item: any) => {
        return item?.type === "row";
      });

      setAnnotations([...corBox, ...itsRow]);
    }
    if (!polyValue.row && !polyValue.box) {
      setAnnotations(imgPolyValue);
    }
  }, [polyValue, imgPolyValue]);

  //GET IMAGE CROP OCR
  const [imageRowList, setImageRowList] = useState<any>([]);
  const [imageBoxList, setImageBoxList] = useState<any>([]);

  const handleOnchangePolygonRow = (value: any, option: any) => {
    if (!value) {
      setSelectedRowPolygonId(null);
      setPolyValue({
        ...polyValue,
        row: null,
      });
    } else {
      setSelectedRowPolygonId({
        value: option?.value,
        label: option?.label,
      });
      setPolyValue({
        ...polyValue,
        row: option?.coordinates,
      });
    }
  };
  const handleOnchangePolygonBox = (value: any, option: any) => {
    if (!value) {
      setSelectedBoxPolygonId(null);
      setPolyValue({
        ...polyValue,
        box: null,
      });
    } else {
      setSelectedBoxPolygonId({
        value: option?.value,
        label: option?.label,
      });
      setPolyValue({
        ...polyValue,
        box: option?.coordinates,
      });
    }
  };
  const [isPanelVisible, setIsPanelVisible] = useState(true);

  const togglePanel = () => {
    setIsPanelVisible(!isPanelVisible);
  };

  //set query filter by search params

  //auto filter
  useEffect(() => {
    if (!queryFilter.projectId) {
      setQueryFilter({
        ...queryFilter,
        projectId: globalProjectId && globalProjectId,
        prospectId: globalProspectId && globalProspectId,
      });
    }
  }, [globalProjectId, globalProspectId]);
  // Update URL parameters when queryFilter changes
  useEffect(() => {
    const params = new URLSearchParams();
    Object.keys(queryFilter).forEach((key) => {
      if (queryFilter[key] !== undefined) {
        params.set(key, queryFilter[key]);
      }
    });
    router.replace(`${window.location.pathname}?${params.toString()}`);
  }, [queryFilter]);

  return (
    <div className="flex gap-1">
      {isPanelVisible && (
        <ProcessImagePanel
          queryFilter={queryFilter}
          setQueryFilter={setQueryFilter}
          refetchDrillhole={refetchDrillhole}
        />
      )}
      <Button
        icon={isPanelVisible ? <FaArrowLeft /> : <FaArrowRight />}
        onClick={togglePanel}
        type="text"
      ></Button>

      {image ? (
        <div className="flex flex-col w-full p-4 items-center">
          <div className="flex items-center px-10 justify-between ">
            <ImageInfo
              img={image}
              queryFilter={queryFilter}
              refetch={refetch}
              setRefetchDrillhole={setRefetchDrillhole}
            />
            <NavigationImage
              img={image}
              total={total}
              queryFilter={queryFilter}
              setQueryFilter={setQueryFilter}
              annotations={annotations}
            />
          </div>
          <DrawImage
            fractures={fractures}
            isAdvance={isAdvance}
            refetch={refetch}
            queryFilter={queryFilter}
            setQueryFilter={setQueryFilter}
            fetchListPolygon={fetchListPolygon}
            breakPoints={breakPoints}
            setBreakPoints={setBreakPoints}
            handleOnchangePolygonBox={handleOnchangePolygonBox}
            handleOnchangePolygonRow={handleOnchangePolygonRow}
            selectedBoxPolygonId={selectedBoxPolygonId}
            selectedRowPolygonId={selectedRowPolygonId}
            optionBoundingBox={optionBoundingBox}
            optionBoundingRow={optionBoundingRow}
            setOptionBoundingRow={setOptionBoundingRow}
            setOptionBoundingBox={setOptionBoundingBox}
            imageConfig={imageConfig}
            setImageConfig={setImageConfig}
            OCRdata={OCRdata}
            directOCRdata={directOCRdata}
            setDirectOCRdata={setDirectOCRdata}
            setOCRdata={setOCRdata}
            imageRowList={imageRowList}
            imageBoxList={imageBoxList}
            img={image}
            annotations={annotations}
            setAnnotations={setAnnotations}
            polyValue={polyValue}
            setPolyValue={setPolyValue}
            showList={showList}
            setShowList={setShowList}
            selectedGroupPolygon={selectedGroupPolygon}
            setSelectedGroupPolygon={setSelectedGroupPolygon}
            originOCRdata={originOCRdata}
            setOriginOCRdata={setOriginOCRdata}
          />
        </div>
      ) : (
        <p className="font-semibold text-xl text-center p-4">No images found</p>
      )}
    </div>
  );
}
